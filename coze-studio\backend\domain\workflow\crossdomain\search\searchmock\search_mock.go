/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by MockGen. DO NOT EDIT.
// Source: search.go
//
// Generated by this command:
//
//	mockgen -destination searchmock/search_mock.go --package searchmock -source search.go
//

// Package searchmock is a generated GoMock package.
package searchmock

import (
	context "context"
	reflect "reflect"

	search "github.com/coze-dev/coze-studio/backend/domain/workflow/crossdomain/search"
	gomock "go.uber.org/mock/gomock"
)

// MockNotifier is a mock of Notifier interface.
type MockNotifier struct {
	ctrl     *gomock.Controller
	recorder *MockNotifierMockRecorder
	isgomock struct{}
}

// MockNotifierMockRecorder is the mock recorder for MockNotifier.
type MockNotifierMockRecorder struct {
	mock *MockNotifier
}

// NewMockNotifier creates a new mock instance.
func NewMockNotifier(ctrl *gomock.Controller) *MockNotifier {
	mock := &MockNotifier{ctrl: ctrl}
	mock.recorder = &MockNotifierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNotifier) EXPECT() *MockNotifierMockRecorder {
	return m.recorder
}

// PublishWorkflowResource mocks base method.
func (m *MockNotifier) PublishWorkflowResource(ctx context.Context, OpType search.OpType, event *search.Resource) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishWorkflowResource", ctx, OpType, event)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishWorkflowResource indicates an expected call of PublishWorkflowResource.
func (mr *MockNotifierMockRecorder) PublishWorkflowResource(ctx, OpType, event any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishWorkflowResource", reflect.TypeOf((*MockNotifier)(nil).PublishWorkflowResource), ctx, OpType, event)
}
