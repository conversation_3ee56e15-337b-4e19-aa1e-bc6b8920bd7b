// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/plugin/internal/dal/model"
)

func newPlugin(db *gorm.DB, opts ...gen.DOOption) plugin {
	_plugin := plugin{}

	_plugin.pluginDo.UseDB(db, opts...)
	_plugin.pluginDo.UseModel(&model.Plugin{})

	tableName := _plugin.pluginDo.TableName()
	_plugin.ALL = field.NewAsterisk(tableName)
	_plugin.ID = field.NewInt64(tableName, "id")
	_plugin.SpaceID = field.NewInt64(tableName, "space_id")
	_plugin.DeveloperID = field.NewInt64(tableName, "developer_id")
	_plugin.AppID = field.NewInt64(tableName, "app_id")
	_plugin.IconURI = field.NewString(tableName, "icon_uri")
	_plugin.ServerURL = field.NewString(tableName, "server_url")
	_plugin.PluginType = field.NewInt32(tableName, "plugin_type")
	_plugin.CreatedAt = field.NewInt64(tableName, "created_at")
	_plugin.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_plugin.Version = field.NewString(tableName, "version")
	_plugin.VersionDesc = field.NewString(tableName, "version_desc")
	_plugin.Manifest = field.NewField(tableName, "manifest")
	_plugin.OpenapiDoc = field.NewField(tableName, "openapi_doc")

	_plugin.fillFieldMap()

	return _plugin
}

// plugin Latest Plugin
type plugin struct {
	pluginDo

	ALL         field.Asterisk
	ID          field.Int64  // Plugin ID
	SpaceID     field.Int64  // Space ID
	DeveloperID field.Int64  // Developer ID
	AppID       field.Int64  // Application ID
	IconURI     field.String // Icon URI
	ServerURL   field.String // Server URL
	PluginType  field.Int32  // Plugin Type, 1:http, 6:local
	CreatedAt   field.Int64  // Create Time in Milliseconds
	UpdatedAt   field.Int64  // Update Time in Milliseconds
	Version     field.String // Plugin Version, e.g. v1.0.0
	VersionDesc field.String // Plugin Version Description
	Manifest    field.Field  // Plugin Manifest
	OpenapiDoc  field.Field  // OpenAPI Document, only stores the root

	fieldMap map[string]field.Expr
}

func (p plugin) Table(newTableName string) *plugin {
	p.pluginDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p plugin) As(alias string) *plugin {
	p.pluginDo.DO = *(p.pluginDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *plugin) updateTableName(table string) *plugin {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.SpaceID = field.NewInt64(table, "space_id")
	p.DeveloperID = field.NewInt64(table, "developer_id")
	p.AppID = field.NewInt64(table, "app_id")
	p.IconURI = field.NewString(table, "icon_uri")
	p.ServerURL = field.NewString(table, "server_url")
	p.PluginType = field.NewInt32(table, "plugin_type")
	p.CreatedAt = field.NewInt64(table, "created_at")
	p.UpdatedAt = field.NewInt64(table, "updated_at")
	p.Version = field.NewString(table, "version")
	p.VersionDesc = field.NewString(table, "version_desc")
	p.Manifest = field.NewField(table, "manifest")
	p.OpenapiDoc = field.NewField(table, "openapi_doc")

	p.fillFieldMap()

	return p
}

func (p *plugin) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *plugin) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 13)
	p.fieldMap["id"] = p.ID
	p.fieldMap["space_id"] = p.SpaceID
	p.fieldMap["developer_id"] = p.DeveloperID
	p.fieldMap["app_id"] = p.AppID
	p.fieldMap["icon_uri"] = p.IconURI
	p.fieldMap["server_url"] = p.ServerURL
	p.fieldMap["plugin_type"] = p.PluginType
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["version"] = p.Version
	p.fieldMap["version_desc"] = p.VersionDesc
	p.fieldMap["manifest"] = p.Manifest
	p.fieldMap["openapi_doc"] = p.OpenapiDoc
}

func (p plugin) clone(db *gorm.DB) plugin {
	p.pluginDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p plugin) replaceDB(db *gorm.DB) plugin {
	p.pluginDo.ReplaceDB(db)
	return p
}

type pluginDo struct{ gen.DO }

type IPluginDo interface {
	gen.SubQuery
	Debug() IPluginDo
	WithContext(ctx context.Context) IPluginDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPluginDo
	WriteDB() IPluginDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPluginDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPluginDo
	Not(conds ...gen.Condition) IPluginDo
	Or(conds ...gen.Condition) IPluginDo
	Select(conds ...field.Expr) IPluginDo
	Where(conds ...gen.Condition) IPluginDo
	Order(conds ...field.Expr) IPluginDo
	Distinct(cols ...field.Expr) IPluginDo
	Omit(cols ...field.Expr) IPluginDo
	Join(table schema.Tabler, on ...field.Expr) IPluginDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPluginDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPluginDo
	Group(cols ...field.Expr) IPluginDo
	Having(conds ...gen.Condition) IPluginDo
	Limit(limit int) IPluginDo
	Offset(offset int) IPluginDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPluginDo
	Unscoped() IPluginDo
	Create(values ...*model.Plugin) error
	CreateInBatches(values []*model.Plugin, batchSize int) error
	Save(values ...*model.Plugin) error
	First() (*model.Plugin, error)
	Take() (*model.Plugin, error)
	Last() (*model.Plugin, error)
	Find() ([]*model.Plugin, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Plugin, err error)
	FindInBatches(result *[]*model.Plugin, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Plugin) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPluginDo
	Assign(attrs ...field.AssignExpr) IPluginDo
	Joins(fields ...field.RelationField) IPluginDo
	Preload(fields ...field.RelationField) IPluginDo
	FirstOrInit() (*model.Plugin, error)
	FirstOrCreate() (*model.Plugin, error)
	FindByPage(offset int, limit int) (result []*model.Plugin, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPluginDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pluginDo) Debug() IPluginDo {
	return p.withDO(p.DO.Debug())
}

func (p pluginDo) WithContext(ctx context.Context) IPluginDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pluginDo) ReadDB() IPluginDo {
	return p.Clauses(dbresolver.Read)
}

func (p pluginDo) WriteDB() IPluginDo {
	return p.Clauses(dbresolver.Write)
}

func (p pluginDo) Session(config *gorm.Session) IPluginDo {
	return p.withDO(p.DO.Session(config))
}

func (p pluginDo) Clauses(conds ...clause.Expression) IPluginDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pluginDo) Returning(value interface{}, columns ...string) IPluginDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pluginDo) Not(conds ...gen.Condition) IPluginDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pluginDo) Or(conds ...gen.Condition) IPluginDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pluginDo) Select(conds ...field.Expr) IPluginDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pluginDo) Where(conds ...gen.Condition) IPluginDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pluginDo) Order(conds ...field.Expr) IPluginDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pluginDo) Distinct(cols ...field.Expr) IPluginDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pluginDo) Omit(cols ...field.Expr) IPluginDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pluginDo) Join(table schema.Tabler, on ...field.Expr) IPluginDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pluginDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPluginDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pluginDo) RightJoin(table schema.Tabler, on ...field.Expr) IPluginDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pluginDo) Group(cols ...field.Expr) IPluginDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pluginDo) Having(conds ...gen.Condition) IPluginDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pluginDo) Limit(limit int) IPluginDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pluginDo) Offset(offset int) IPluginDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pluginDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPluginDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pluginDo) Unscoped() IPluginDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pluginDo) Create(values ...*model.Plugin) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pluginDo) CreateInBatches(values []*model.Plugin, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pluginDo) Save(values ...*model.Plugin) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pluginDo) First() (*model.Plugin, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Plugin), nil
	}
}

func (p pluginDo) Take() (*model.Plugin, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Plugin), nil
	}
}

func (p pluginDo) Last() (*model.Plugin, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Plugin), nil
	}
}

func (p pluginDo) Find() ([]*model.Plugin, error) {
	result, err := p.DO.Find()
	return result.([]*model.Plugin), err
}

func (p pluginDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Plugin, err error) {
	buf := make([]*model.Plugin, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pluginDo) FindInBatches(result *[]*model.Plugin, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pluginDo) Attrs(attrs ...field.AssignExpr) IPluginDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pluginDo) Assign(attrs ...field.AssignExpr) IPluginDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pluginDo) Joins(fields ...field.RelationField) IPluginDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pluginDo) Preload(fields ...field.RelationField) IPluginDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pluginDo) FirstOrInit() (*model.Plugin, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Plugin), nil
	}
}

func (p pluginDo) FirstOrCreate() (*model.Plugin, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Plugin), nil
	}
}

func (p pluginDo) FindByPage(offset int, limit int) (result []*model.Plugin, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pluginDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pluginDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pluginDo) Delete(models ...*model.Plugin) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pluginDo) withDO(do gen.Dao) *pluginDo {
	p.DO = *do.(*gen.DO)
	return p
}
