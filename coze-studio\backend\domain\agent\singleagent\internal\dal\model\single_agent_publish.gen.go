// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameSingleAgentPublish = "single_agent_publish"

// SingleAgentPublish bot 渠道和发布版本流水表
type SingleAgentPublish struct {
	ID           int64   `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键id" json:"id"`                                        // 主键id
	AgentID      int64   `gorm:"column:agent_id;not null;comment:agent_id" json:"agent_id"`                                             // agent_id
	PublishID    string  `gorm:"column:publish_id;not null;comment:发布 id" json:"publish_id"`                                            // 发布 id
	ConnectorIds []int64 `gorm:"column:connector_ids;comment:发布的 connector_ids;serializer:json" json:"connector_ids"`                   // 发布的 connector_ids
	Version      string  `gorm:"column:version;not null;comment:Agent Version" json:"version"`                                          // Agent Version
	PublishInfo  *string `gorm:"column:publish_info;comment:发布信息" json:"publish_info"`                                                  // 发布信息
	PublishTime  int64   `gorm:"column:publish_time;not null;comment:发布时间" json:"publish_time"`                                         // 发布时间
	CreatedAt    int64   `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	UpdatedAt    int64   `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"` // Update Time in Milliseconds
	CreatorID    int64   `gorm:"column:creator_id;not null;comment:发布人 user_id" json:"creator_id"`                                      // 发布人 user_id
	Status       int32   `gorm:"column:status;not null;comment:状态 0:使用中 1:删除 3:禁用" json:"status"`                                       // 状态 0:使用中 1:删除 3:禁用
	Extra        *string `gorm:"column:extra;comment:扩展字段" json:"extra"`                                                                // 扩展字段
}

// TableName SingleAgentPublish's table name
func (*SingleAgentPublish) TableName() string {
	return TableNameSingleAgentPublish
}
