// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/plugin/internal/dal/model"
)

func newAgentToolVersion(db *gorm.DB, opts ...gen.DOOption) agentToolVersion {
	_agentToolVersion := agentToolVersion{}

	_agentToolVersion.agentToolVersionDo.UseDB(db, opts...)
	_agentToolVersion.agentToolVersionDo.UseModel(&model.AgentToolVersion{})

	tableName := _agentToolVersion.agentToolVersionDo.TableName()
	_agentToolVersion.ALL = field.NewAsterisk(tableName)
	_agentToolVersion.ID = field.NewInt64(tableName, "id")
	_agentToolVersion.AgentID = field.NewInt64(tableName, "agent_id")
	_agentToolVersion.PluginID = field.NewInt64(tableName, "plugin_id")
	_agentToolVersion.ToolID = field.NewInt64(tableName, "tool_id")
	_agentToolVersion.AgentVersion = field.NewString(tableName, "agent_version")
	_agentToolVersion.ToolName = field.NewString(tableName, "tool_name")
	_agentToolVersion.ToolVersion = field.NewString(tableName, "tool_version")
	_agentToolVersion.SubURL = field.NewString(tableName, "sub_url")
	_agentToolVersion.Method = field.NewString(tableName, "method")
	_agentToolVersion.Operation = field.NewField(tableName, "operation")
	_agentToolVersion.CreatedAt = field.NewInt64(tableName, "created_at")

	_agentToolVersion.fillFieldMap()

	return _agentToolVersion
}

// agentToolVersion Agent Tool Version
type agentToolVersion struct {
	agentToolVersionDo

	ALL          field.Asterisk
	ID           field.Int64  // Primary Key ID
	AgentID      field.Int64  // Agent ID
	PluginID     field.Int64  // Plugin ID
	ToolID       field.Int64  // Tool ID
	AgentVersion field.String // Agent Tool Version
	ToolName     field.String // Tool Name
	ToolVersion  field.String // Tool Version, e.g. v1.0.0
	SubURL       field.String // Sub URL Path
	Method       field.String // HTTP Request Method
	Operation    field.Field  // Tool Openapi Operation Schema
	CreatedAt    field.Int64  // Create Time in Milliseconds

	fieldMap map[string]field.Expr
}

func (a agentToolVersion) Table(newTableName string) *agentToolVersion {
	a.agentToolVersionDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a agentToolVersion) As(alias string) *agentToolVersion {
	a.agentToolVersionDo.DO = *(a.agentToolVersionDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *agentToolVersion) updateTableName(table string) *agentToolVersion {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.AgentID = field.NewInt64(table, "agent_id")
	a.PluginID = field.NewInt64(table, "plugin_id")
	a.ToolID = field.NewInt64(table, "tool_id")
	a.AgentVersion = field.NewString(table, "agent_version")
	a.ToolName = field.NewString(table, "tool_name")
	a.ToolVersion = field.NewString(table, "tool_version")
	a.SubURL = field.NewString(table, "sub_url")
	a.Method = field.NewString(table, "method")
	a.Operation = field.NewField(table, "operation")
	a.CreatedAt = field.NewInt64(table, "created_at")

	a.fillFieldMap()

	return a
}

func (a *agentToolVersion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *agentToolVersion) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 11)
	a.fieldMap["id"] = a.ID
	a.fieldMap["agent_id"] = a.AgentID
	a.fieldMap["plugin_id"] = a.PluginID
	a.fieldMap["tool_id"] = a.ToolID
	a.fieldMap["agent_version"] = a.AgentVersion
	a.fieldMap["tool_name"] = a.ToolName
	a.fieldMap["tool_version"] = a.ToolVersion
	a.fieldMap["sub_url"] = a.SubURL
	a.fieldMap["method"] = a.Method
	a.fieldMap["operation"] = a.Operation
	a.fieldMap["created_at"] = a.CreatedAt
}

func (a agentToolVersion) clone(db *gorm.DB) agentToolVersion {
	a.agentToolVersionDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a agentToolVersion) replaceDB(db *gorm.DB) agentToolVersion {
	a.agentToolVersionDo.ReplaceDB(db)
	return a
}

type agentToolVersionDo struct{ gen.DO }

type IAgentToolVersionDo interface {
	gen.SubQuery
	Debug() IAgentToolVersionDo
	WithContext(ctx context.Context) IAgentToolVersionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAgentToolVersionDo
	WriteDB() IAgentToolVersionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAgentToolVersionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAgentToolVersionDo
	Not(conds ...gen.Condition) IAgentToolVersionDo
	Or(conds ...gen.Condition) IAgentToolVersionDo
	Select(conds ...field.Expr) IAgentToolVersionDo
	Where(conds ...gen.Condition) IAgentToolVersionDo
	Order(conds ...field.Expr) IAgentToolVersionDo
	Distinct(cols ...field.Expr) IAgentToolVersionDo
	Omit(cols ...field.Expr) IAgentToolVersionDo
	Join(table schema.Tabler, on ...field.Expr) IAgentToolVersionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAgentToolVersionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAgentToolVersionDo
	Group(cols ...field.Expr) IAgentToolVersionDo
	Having(conds ...gen.Condition) IAgentToolVersionDo
	Limit(limit int) IAgentToolVersionDo
	Offset(offset int) IAgentToolVersionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAgentToolVersionDo
	Unscoped() IAgentToolVersionDo
	Create(values ...*model.AgentToolVersion) error
	CreateInBatches(values []*model.AgentToolVersion, batchSize int) error
	Save(values ...*model.AgentToolVersion) error
	First() (*model.AgentToolVersion, error)
	Take() (*model.AgentToolVersion, error)
	Last() (*model.AgentToolVersion, error)
	Find() ([]*model.AgentToolVersion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AgentToolVersion, err error)
	FindInBatches(result *[]*model.AgentToolVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AgentToolVersion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAgentToolVersionDo
	Assign(attrs ...field.AssignExpr) IAgentToolVersionDo
	Joins(fields ...field.RelationField) IAgentToolVersionDo
	Preload(fields ...field.RelationField) IAgentToolVersionDo
	FirstOrInit() (*model.AgentToolVersion, error)
	FirstOrCreate() (*model.AgentToolVersion, error)
	FindByPage(offset int, limit int) (result []*model.AgentToolVersion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAgentToolVersionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a agentToolVersionDo) Debug() IAgentToolVersionDo {
	return a.withDO(a.DO.Debug())
}

func (a agentToolVersionDo) WithContext(ctx context.Context) IAgentToolVersionDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a agentToolVersionDo) ReadDB() IAgentToolVersionDo {
	return a.Clauses(dbresolver.Read)
}

func (a agentToolVersionDo) WriteDB() IAgentToolVersionDo {
	return a.Clauses(dbresolver.Write)
}

func (a agentToolVersionDo) Session(config *gorm.Session) IAgentToolVersionDo {
	return a.withDO(a.DO.Session(config))
}

func (a agentToolVersionDo) Clauses(conds ...clause.Expression) IAgentToolVersionDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a agentToolVersionDo) Returning(value interface{}, columns ...string) IAgentToolVersionDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a agentToolVersionDo) Not(conds ...gen.Condition) IAgentToolVersionDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a agentToolVersionDo) Or(conds ...gen.Condition) IAgentToolVersionDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a agentToolVersionDo) Select(conds ...field.Expr) IAgentToolVersionDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a agentToolVersionDo) Where(conds ...gen.Condition) IAgentToolVersionDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a agentToolVersionDo) Order(conds ...field.Expr) IAgentToolVersionDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a agentToolVersionDo) Distinct(cols ...field.Expr) IAgentToolVersionDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a agentToolVersionDo) Omit(cols ...field.Expr) IAgentToolVersionDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a agentToolVersionDo) Join(table schema.Tabler, on ...field.Expr) IAgentToolVersionDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a agentToolVersionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAgentToolVersionDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a agentToolVersionDo) RightJoin(table schema.Tabler, on ...field.Expr) IAgentToolVersionDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a agentToolVersionDo) Group(cols ...field.Expr) IAgentToolVersionDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a agentToolVersionDo) Having(conds ...gen.Condition) IAgentToolVersionDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a agentToolVersionDo) Limit(limit int) IAgentToolVersionDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a agentToolVersionDo) Offset(offset int) IAgentToolVersionDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a agentToolVersionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAgentToolVersionDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a agentToolVersionDo) Unscoped() IAgentToolVersionDo {
	return a.withDO(a.DO.Unscoped())
}

func (a agentToolVersionDo) Create(values ...*model.AgentToolVersion) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a agentToolVersionDo) CreateInBatches(values []*model.AgentToolVersion, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a agentToolVersionDo) Save(values ...*model.AgentToolVersion) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a agentToolVersionDo) First() (*model.AgentToolVersion, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AgentToolVersion), nil
	}
}

func (a agentToolVersionDo) Take() (*model.AgentToolVersion, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AgentToolVersion), nil
	}
}

func (a agentToolVersionDo) Last() (*model.AgentToolVersion, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AgentToolVersion), nil
	}
}

func (a agentToolVersionDo) Find() ([]*model.AgentToolVersion, error) {
	result, err := a.DO.Find()
	return result.([]*model.AgentToolVersion), err
}

func (a agentToolVersionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AgentToolVersion, err error) {
	buf := make([]*model.AgentToolVersion, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a agentToolVersionDo) FindInBatches(result *[]*model.AgentToolVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a agentToolVersionDo) Attrs(attrs ...field.AssignExpr) IAgentToolVersionDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a agentToolVersionDo) Assign(attrs ...field.AssignExpr) IAgentToolVersionDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a agentToolVersionDo) Joins(fields ...field.RelationField) IAgentToolVersionDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a agentToolVersionDo) Preload(fields ...field.RelationField) IAgentToolVersionDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a agentToolVersionDo) FirstOrInit() (*model.AgentToolVersion, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AgentToolVersion), nil
	}
}

func (a agentToolVersionDo) FirstOrCreate() (*model.AgentToolVersion, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AgentToolVersion), nil
	}
}

func (a agentToolVersionDo) FindByPage(offset int, limit int) (result []*model.AgentToolVersion, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a agentToolVersionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a agentToolVersionDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a agentToolVersionDo) Delete(models ...*model.AgentToolVersion) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *agentToolVersionDo) withDO(do gen.Dao) *agentToolVersionDo {
	a.DO = *do.(*gen.DO)
	return a
}
