/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by MockGen. DO NOT EDIT.
// Source: plugin.go
//
// Generated by this command:
//
//	mockgen -destination pluginmock/plugin_mock.go --package pluginmock -source plugin.go
//

// Package pluginmock is a generated GoMock package.
package pluginmock

import (
	context "context"
	reflect "reflect"

	plugin "github.com/coze-dev/coze-studio/backend/domain/workflow/crossdomain/plugin"
	schema "github.com/cloudwego/eino/schema"
	gomock "go.uber.org/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
	isgomock struct{}
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// ExecutePlugin mocks base method.
func (m *MockService) ExecutePlugin(ctx context.Context, input map[string]any, pe *plugin.Entity, toolID int64, cfg plugin.ExecConfig) (map[string]any, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecutePlugin", ctx, input, pe, toolID, cfg)
	ret0, _ := ret[0].(map[string]any)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecutePlugin indicates an expected call of ExecutePlugin.
func (mr *MockServiceMockRecorder) ExecutePlugin(ctx, input, pe, toolID, cfg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecutePlugin", reflect.TypeOf((*MockService)(nil).ExecutePlugin), ctx, input, pe, toolID, cfg)
}

// GetPluginInvokableTools mocks base method.
func (m *MockService) GetPluginInvokableTools(ctx context.Context, req *plugin.ToolsInvokableRequest) (map[int64]plugin.InvokableTool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPluginInvokableTools", ctx, req)
	ret0, _ := ret[0].(map[int64]plugin.InvokableTool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPluginInvokableTools indicates an expected call of GetPluginInvokableTools.
func (mr *MockServiceMockRecorder) GetPluginInvokableTools(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPluginInvokableTools", reflect.TypeOf((*MockService)(nil).GetPluginInvokableTools), ctx, req)
}

// GetPluginToolsInfo mocks base method.
func (m *MockService) GetPluginToolsInfo(ctx context.Context, req *plugin.ToolsInfoRequest) (*plugin.ToolsInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPluginToolsInfo", ctx, req)
	ret0, _ := ret[0].(*plugin.ToolsInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPluginToolsInfo indicates an expected call of GetPluginToolsInfo.
func (mr *MockServiceMockRecorder) GetPluginToolsInfo(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPluginToolsInfo", reflect.TypeOf((*MockService)(nil).GetPluginToolsInfo), ctx, req)
}

// MockInvokableTool is a mock of InvokableTool interface.
type MockInvokableTool struct {
	ctrl     *gomock.Controller
	recorder *MockInvokableToolMockRecorder
	isgomock struct{}
}

// MockInvokableToolMockRecorder is the mock recorder for MockInvokableTool.
type MockInvokableToolMockRecorder struct {
	mock *MockInvokableTool
}

// NewMockInvokableTool creates a new mock instance.
func NewMockInvokableTool(ctrl *gomock.Controller) *MockInvokableTool {
	mock := &MockInvokableTool{ctrl: ctrl}
	mock.recorder = &MockInvokableToolMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInvokableTool) EXPECT() *MockInvokableToolMockRecorder {
	return m.recorder
}

// Info mocks base method.
func (m *MockInvokableTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Info", ctx)
	ret0, _ := ret[0].(*schema.ToolInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Info indicates an expected call of Info.
func (mr *MockInvokableToolMockRecorder) Info(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Info", reflect.TypeOf((*MockInvokableTool)(nil).Info), ctx)
}

// PluginInvoke mocks base method.
func (m *MockInvokableTool) PluginInvoke(ctx context.Context, argumentsInJSON string, cfg plugin.ExecConfig) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PluginInvoke", ctx, argumentsInJSON, cfg)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PluginInvoke indicates an expected call of PluginInvoke.
func (mr *MockInvokableToolMockRecorder) PluginInvoke(ctx, argumentsInJSON, cfg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PluginInvoke", reflect.TypeOf((*MockInvokableTool)(nil).PluginInvoke), ctx, argumentsInJSON, cfg)
}
