// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/plugin/internal/dal/model"
)

func newTool(db *gorm.DB, opts ...gen.DOOption) tool {
	_tool := tool{}

	_tool.toolDo.UseDB(db, opts...)
	_tool.toolDo.UseModel(&model.Tool{})

	tableName := _tool.toolDo.TableName()
	_tool.ALL = field.NewAsterisk(tableName)
	_tool.ID = field.NewInt64(tableName, "id")
	_tool.PluginID = field.NewInt64(tableName, "plugin_id")
	_tool.CreatedAt = field.NewInt64(tableName, "created_at")
	_tool.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_tool.Version = field.NewString(tableName, "version")
	_tool.SubURL = field.NewString(tableName, "sub_url")
	_tool.Method = field.NewString(tableName, "method")
	_tool.Operation = field.NewField(tableName, "operation")
	_tool.ActivatedStatus = field.NewInt32(tableName, "activated_status")

	_tool.fillFieldMap()

	return _tool
}

// tool Latest Tool
type tool struct {
	toolDo

	ALL             field.Asterisk
	ID              field.Int64  // Tool ID
	PluginID        field.Int64  // Plugin ID
	CreatedAt       field.Int64  // Create Time in Milliseconds
	UpdatedAt       field.Int64  // Update Time in Milliseconds
	Version         field.String // Tool Version, e.g. v1.0.0
	SubURL          field.String // Sub URL Path
	Method          field.String // HTTP Request Method
	Operation       field.Field  // Tool Openapi Operation Schema
	ActivatedStatus field.Int32  // 0:activated; 1:deactivated

	fieldMap map[string]field.Expr
}

func (t tool) Table(newTableName string) *tool {
	t.toolDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tool) As(alias string) *tool {
	t.toolDo.DO = *(t.toolDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tool) updateTableName(table string) *tool {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt64(table, "id")
	t.PluginID = field.NewInt64(table, "plugin_id")
	t.CreatedAt = field.NewInt64(table, "created_at")
	t.UpdatedAt = field.NewInt64(table, "updated_at")
	t.Version = field.NewString(table, "version")
	t.SubURL = field.NewString(table, "sub_url")
	t.Method = field.NewString(table, "method")
	t.Operation = field.NewField(table, "operation")
	t.ActivatedStatus = field.NewInt32(table, "activated_status")

	t.fillFieldMap()

	return t
}

func (t *tool) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tool) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 9)
	t.fieldMap["id"] = t.ID
	t.fieldMap["plugin_id"] = t.PluginID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["version"] = t.Version
	t.fieldMap["sub_url"] = t.SubURL
	t.fieldMap["method"] = t.Method
	t.fieldMap["operation"] = t.Operation
	t.fieldMap["activated_status"] = t.ActivatedStatus
}

func (t tool) clone(db *gorm.DB) tool {
	t.toolDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tool) replaceDB(db *gorm.DB) tool {
	t.toolDo.ReplaceDB(db)
	return t
}

type toolDo struct{ gen.DO }

type IToolDo interface {
	gen.SubQuery
	Debug() IToolDo
	WithContext(ctx context.Context) IToolDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IToolDo
	WriteDB() IToolDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IToolDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IToolDo
	Not(conds ...gen.Condition) IToolDo
	Or(conds ...gen.Condition) IToolDo
	Select(conds ...field.Expr) IToolDo
	Where(conds ...gen.Condition) IToolDo
	Order(conds ...field.Expr) IToolDo
	Distinct(cols ...field.Expr) IToolDo
	Omit(cols ...field.Expr) IToolDo
	Join(table schema.Tabler, on ...field.Expr) IToolDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IToolDo
	RightJoin(table schema.Tabler, on ...field.Expr) IToolDo
	Group(cols ...field.Expr) IToolDo
	Having(conds ...gen.Condition) IToolDo
	Limit(limit int) IToolDo
	Offset(offset int) IToolDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IToolDo
	Unscoped() IToolDo
	Create(values ...*model.Tool) error
	CreateInBatches(values []*model.Tool, batchSize int) error
	Save(values ...*model.Tool) error
	First() (*model.Tool, error)
	Take() (*model.Tool, error)
	Last() (*model.Tool, error)
	Find() ([]*model.Tool, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Tool, err error)
	FindInBatches(result *[]*model.Tool, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Tool) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IToolDo
	Assign(attrs ...field.AssignExpr) IToolDo
	Joins(fields ...field.RelationField) IToolDo
	Preload(fields ...field.RelationField) IToolDo
	FirstOrInit() (*model.Tool, error)
	FirstOrCreate() (*model.Tool, error)
	FindByPage(offset int, limit int) (result []*model.Tool, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IToolDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t toolDo) Debug() IToolDo {
	return t.withDO(t.DO.Debug())
}

func (t toolDo) WithContext(ctx context.Context) IToolDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t toolDo) ReadDB() IToolDo {
	return t.Clauses(dbresolver.Read)
}

func (t toolDo) WriteDB() IToolDo {
	return t.Clauses(dbresolver.Write)
}

func (t toolDo) Session(config *gorm.Session) IToolDo {
	return t.withDO(t.DO.Session(config))
}

func (t toolDo) Clauses(conds ...clause.Expression) IToolDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t toolDo) Returning(value interface{}, columns ...string) IToolDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t toolDo) Not(conds ...gen.Condition) IToolDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t toolDo) Or(conds ...gen.Condition) IToolDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t toolDo) Select(conds ...field.Expr) IToolDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t toolDo) Where(conds ...gen.Condition) IToolDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t toolDo) Order(conds ...field.Expr) IToolDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t toolDo) Distinct(cols ...field.Expr) IToolDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t toolDo) Omit(cols ...field.Expr) IToolDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t toolDo) Join(table schema.Tabler, on ...field.Expr) IToolDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t toolDo) LeftJoin(table schema.Tabler, on ...field.Expr) IToolDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t toolDo) RightJoin(table schema.Tabler, on ...field.Expr) IToolDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t toolDo) Group(cols ...field.Expr) IToolDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t toolDo) Having(conds ...gen.Condition) IToolDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t toolDo) Limit(limit int) IToolDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t toolDo) Offset(offset int) IToolDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t toolDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IToolDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t toolDo) Unscoped() IToolDo {
	return t.withDO(t.DO.Unscoped())
}

func (t toolDo) Create(values ...*model.Tool) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t toolDo) CreateInBatches(values []*model.Tool, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t toolDo) Save(values ...*model.Tool) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t toolDo) First() (*model.Tool, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Tool), nil
	}
}

func (t toolDo) Take() (*model.Tool, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Tool), nil
	}
}

func (t toolDo) Last() (*model.Tool, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Tool), nil
	}
}

func (t toolDo) Find() ([]*model.Tool, error) {
	result, err := t.DO.Find()
	return result.([]*model.Tool), err
}

func (t toolDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Tool, err error) {
	buf := make([]*model.Tool, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t toolDo) FindInBatches(result *[]*model.Tool, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t toolDo) Attrs(attrs ...field.AssignExpr) IToolDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t toolDo) Assign(attrs ...field.AssignExpr) IToolDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t toolDo) Joins(fields ...field.RelationField) IToolDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t toolDo) Preload(fields ...field.RelationField) IToolDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t toolDo) FirstOrInit() (*model.Tool, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Tool), nil
	}
}

func (t toolDo) FirstOrCreate() (*model.Tool, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Tool), nil
	}
}

func (t toolDo) FindByPage(offset int, limit int) (result []*model.Tool, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t toolDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t toolDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t toolDo) Delete(models ...*model.Tool) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *toolDo) withDO(do gen.Dao) *toolDo {
	t.DO = *do.(*gen.DO)
	return t
}
