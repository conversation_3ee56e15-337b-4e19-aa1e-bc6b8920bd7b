/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package vo

import "time"

type VersionInfo struct {
	*VersionMeta

	CanvasInfo

	CommitID string
}

type PublishPolicy struct {
	ID                 int64
	Version            string
	VersionDescription string
	CreatorID          int64
	CommitID           string
	Force              bool
}

type VersionMeta struct {
	Version            string
	VersionDescription string
	VersionCreatedAt   time.Time
	VersionCreatorID   int64
}
