// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/user/internal/dal/model"
)

func newSpaceUser(db *gorm.DB, opts ...gen.DOOption) spaceUser {
	_spaceUser := spaceUser{}

	_spaceUser.spaceUserDo.UseDB(db, opts...)
	_spaceUser.spaceUserDo.UseModel(&model.SpaceUser{})

	tableName := _spaceUser.spaceUserDo.TableName()
	_spaceUser.ALL = field.NewAsterisk(tableName)
	_spaceUser.ID = field.NewInt64(tableName, "id")
	_spaceUser.SpaceID = field.NewInt64(tableName, "space_id")
	_spaceUser.UserID = field.NewInt64(tableName, "user_id")
	_spaceUser.RoleType = field.NewInt32(tableName, "role_type")
	_spaceUser.CreatedAt = field.NewInt64(tableName, "created_at")
	_spaceUser.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_spaceUser.fillFieldMap()

	return _spaceUser
}

// spaceUser Space Member Table
type spaceUser struct {
	spaceUserDo

	ALL       field.Asterisk
	ID        field.Int64 // Primary Key ID, Auto Increment
	SpaceID   field.Int64 // Space ID
	UserID    field.Int64 // User ID
	RoleType  field.Int32 // Role Type: 1.owner 2.admin 3.member
	CreatedAt field.Int64 // Creation Time (Milliseconds)
	UpdatedAt field.Int64 // Update Time (Milliseconds)

	fieldMap map[string]field.Expr
}

func (s spaceUser) Table(newTableName string) *spaceUser {
	s.spaceUserDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s spaceUser) As(alias string) *spaceUser {
	s.spaceUserDo.DO = *(s.spaceUserDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *spaceUser) updateTableName(table string) *spaceUser {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.SpaceID = field.NewInt64(table, "space_id")
	s.UserID = field.NewInt64(table, "user_id")
	s.RoleType = field.NewInt32(table, "role_type")
	s.CreatedAt = field.NewInt64(table, "created_at")
	s.UpdatedAt = field.NewInt64(table, "updated_at")

	s.fillFieldMap()

	return s
}

func (s *spaceUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *spaceUser) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 6)
	s.fieldMap["id"] = s.ID
	s.fieldMap["space_id"] = s.SpaceID
	s.fieldMap["user_id"] = s.UserID
	s.fieldMap["role_type"] = s.RoleType
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
}

func (s spaceUser) clone(db *gorm.DB) spaceUser {
	s.spaceUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s spaceUser) replaceDB(db *gorm.DB) spaceUser {
	s.spaceUserDo.ReplaceDB(db)
	return s
}

type spaceUserDo struct{ gen.DO }

type ISpaceUserDo interface {
	gen.SubQuery
	Debug() ISpaceUserDo
	WithContext(ctx context.Context) ISpaceUserDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISpaceUserDo
	WriteDB() ISpaceUserDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISpaceUserDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISpaceUserDo
	Not(conds ...gen.Condition) ISpaceUserDo
	Or(conds ...gen.Condition) ISpaceUserDo
	Select(conds ...field.Expr) ISpaceUserDo
	Where(conds ...gen.Condition) ISpaceUserDo
	Order(conds ...field.Expr) ISpaceUserDo
	Distinct(cols ...field.Expr) ISpaceUserDo
	Omit(cols ...field.Expr) ISpaceUserDo
	Join(table schema.Tabler, on ...field.Expr) ISpaceUserDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISpaceUserDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISpaceUserDo
	Group(cols ...field.Expr) ISpaceUserDo
	Having(conds ...gen.Condition) ISpaceUserDo
	Limit(limit int) ISpaceUserDo
	Offset(offset int) ISpaceUserDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISpaceUserDo
	Unscoped() ISpaceUserDo
	Create(values ...*model.SpaceUser) error
	CreateInBatches(values []*model.SpaceUser, batchSize int) error
	Save(values ...*model.SpaceUser) error
	First() (*model.SpaceUser, error)
	Take() (*model.SpaceUser, error)
	Last() (*model.SpaceUser, error)
	Find() ([]*model.SpaceUser, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SpaceUser, err error)
	FindInBatches(result *[]*model.SpaceUser, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SpaceUser) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISpaceUserDo
	Assign(attrs ...field.AssignExpr) ISpaceUserDo
	Joins(fields ...field.RelationField) ISpaceUserDo
	Preload(fields ...field.RelationField) ISpaceUserDo
	FirstOrInit() (*model.SpaceUser, error)
	FirstOrCreate() (*model.SpaceUser, error)
	FindByPage(offset int, limit int) (result []*model.SpaceUser, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISpaceUserDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s spaceUserDo) Debug() ISpaceUserDo {
	return s.withDO(s.DO.Debug())
}

func (s spaceUserDo) WithContext(ctx context.Context) ISpaceUserDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s spaceUserDo) ReadDB() ISpaceUserDo {
	return s.Clauses(dbresolver.Read)
}

func (s spaceUserDo) WriteDB() ISpaceUserDo {
	return s.Clauses(dbresolver.Write)
}

func (s spaceUserDo) Session(config *gorm.Session) ISpaceUserDo {
	return s.withDO(s.DO.Session(config))
}

func (s spaceUserDo) Clauses(conds ...clause.Expression) ISpaceUserDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s spaceUserDo) Returning(value interface{}, columns ...string) ISpaceUserDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s spaceUserDo) Not(conds ...gen.Condition) ISpaceUserDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s spaceUserDo) Or(conds ...gen.Condition) ISpaceUserDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s spaceUserDo) Select(conds ...field.Expr) ISpaceUserDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s spaceUserDo) Where(conds ...gen.Condition) ISpaceUserDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s spaceUserDo) Order(conds ...field.Expr) ISpaceUserDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s spaceUserDo) Distinct(cols ...field.Expr) ISpaceUserDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s spaceUserDo) Omit(cols ...field.Expr) ISpaceUserDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s spaceUserDo) Join(table schema.Tabler, on ...field.Expr) ISpaceUserDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s spaceUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISpaceUserDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s spaceUserDo) RightJoin(table schema.Tabler, on ...field.Expr) ISpaceUserDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s spaceUserDo) Group(cols ...field.Expr) ISpaceUserDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s spaceUserDo) Having(conds ...gen.Condition) ISpaceUserDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s spaceUserDo) Limit(limit int) ISpaceUserDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s spaceUserDo) Offset(offset int) ISpaceUserDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s spaceUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISpaceUserDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s spaceUserDo) Unscoped() ISpaceUserDo {
	return s.withDO(s.DO.Unscoped())
}

func (s spaceUserDo) Create(values ...*model.SpaceUser) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s spaceUserDo) CreateInBatches(values []*model.SpaceUser, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s spaceUserDo) Save(values ...*model.SpaceUser) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s spaceUserDo) First() (*model.SpaceUser, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SpaceUser), nil
	}
}

func (s spaceUserDo) Take() (*model.SpaceUser, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SpaceUser), nil
	}
}

func (s spaceUserDo) Last() (*model.SpaceUser, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SpaceUser), nil
	}
}

func (s spaceUserDo) Find() ([]*model.SpaceUser, error) {
	result, err := s.DO.Find()
	return result.([]*model.SpaceUser), err
}

func (s spaceUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SpaceUser, err error) {
	buf := make([]*model.SpaceUser, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s spaceUserDo) FindInBatches(result *[]*model.SpaceUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s spaceUserDo) Attrs(attrs ...field.AssignExpr) ISpaceUserDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s spaceUserDo) Assign(attrs ...field.AssignExpr) ISpaceUserDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s spaceUserDo) Joins(fields ...field.RelationField) ISpaceUserDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s spaceUserDo) Preload(fields ...field.RelationField) ISpaceUserDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s spaceUserDo) FirstOrInit() (*model.SpaceUser, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SpaceUser), nil
	}
}

func (s spaceUserDo) FirstOrCreate() (*model.SpaceUser, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SpaceUser), nil
	}
}

func (s spaceUserDo) FindByPage(offset int, limit int) (result []*model.SpaceUser, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s spaceUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s spaceUserDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s spaceUserDo) Delete(models ...*model.SpaceUser) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *spaceUserDo) withDO(do gen.Dao) *spaceUserDo {
	s.DO = *do.(*gen.DO)
	return s
}
