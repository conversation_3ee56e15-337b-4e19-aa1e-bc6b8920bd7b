2025/07/29-13:52:36.531956 46 RocksDB version: 6.29.5
2025/07/29-13:52:36.532569 46 Git sha 0
2025/07/29-13:52:36.532574 46 Compile date 2024-11-15 11:22:58
2025/07/29-13:52:36.532581 46 DB SUMMARY
2025/07/29-13:52:36.532582 46 DB Session ID:  JB94WLAEXWJD2OQZDUAA
2025/07/29-13:52:36.534333 46 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 0, files: 
2025/07/29-13:52:36.534343 46 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 
2025/07/29-13:52:36.534348 46                         Options.error_if_exists: 0
2025/07/29-13:52:36.534349 46                       Options.create_if_missing: 1
2025/07/29-13:52:36.534349 46                         Options.paranoid_checks: 1
2025/07/29-13:52:36.534350 46             Options.flush_verify_memtable_count: 1
2025/07/29-13:52:36.534351 46                               Options.track_and_verify_wals_in_manifest: 0
2025/07/29-13:52:36.534351 46                                     Options.env: 0x7ea4129aad00
2025/07/29-13:52:36.534352 46                                      Options.fs: PosixFileSystem
2025/07/29-13:52:36.534353 46                                Options.info_log: 0x7ea334c90050
2025/07/29-13:52:36.534354 46                Options.max_file_opening_threads: 16
2025/07/29-13:52:36.534354 46                              Options.statistics: (nil)
2025/07/29-13:52:36.534355 46                               Options.use_fsync: 0
2025/07/29-13:52:36.534356 46                       Options.max_log_file_size: 0
2025/07/29-13:52:36.534356 46                  Options.max_manifest_file_size: 1073741824
2025/07/29-13:52:36.534357 46                   Options.log_file_time_to_roll: 0
2025/07/29-13:52:36.534357 46                       Options.keep_log_file_num: 1000
2025/07/29-13:52:36.534358 46                    Options.recycle_log_file_num: 0
2025/07/29-13:52:36.534358 46                         Options.allow_fallocate: 1
2025/07/29-13:52:36.534359 46                        Options.allow_mmap_reads: 0
2025/07/29-13:52:36.534359 46                       Options.allow_mmap_writes: 0
2025/07/29-13:52:36.534360 46                        Options.use_direct_reads: 0
2025/07/29-13:52:36.534360 46                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/29-13:52:36.534361 46          Options.create_missing_column_families: 0
2025/07/29-13:52:36.534361 46                              Options.db_log_dir: 
2025/07/29-13:52:36.534362 46                                 Options.wal_dir: 
2025/07/29-13:52:36.534362 46                Options.table_cache_numshardbits: 6
2025/07/29-13:52:36.534363 46                         Options.WAL_ttl_seconds: 0
2025/07/29-13:52:36.534363 46                       Options.WAL_size_limit_MB: 0
2025/07/29-13:52:36.534364 46                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/29-13:52:36.534364 46             Options.manifest_preallocation_size: 4194304
2025/07/29-13:52:36.534365 46                     Options.is_fd_close_on_exec: 1
2025/07/29-13:52:36.534365 46                   Options.advise_random_on_open: 1
2025/07/29-13:52:36.534366 46                   Options.experimental_mempurge_threshold: 0.000000
2025/07/29-13:52:36.534377 46                    Options.db_write_buffer_size: 0
2025/07/29-13:52:36.534377 46                    Options.write_buffer_manager: 0x7ea3376400a0
2025/07/29-13:52:36.534378 46         Options.access_hint_on_compaction_start: 1
2025/07/29-13:52:36.534378 46  Options.new_table_reader_for_compaction_inputs: 0
2025/07/29-13:52:36.534379 46           Options.random_access_max_buffer_size: 1048576
2025/07/29-13:52:36.534379 46                      Options.use_adaptive_mutex: 0
2025/07/29-13:52:36.534380 46                            Options.rate_limiter: (nil)
2025/07/29-13:52:36.534383 46     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/29-13:52:36.534384 46                       Options.wal_recovery_mode: 2
2025/07/29-13:52:36.534384 46                  Options.enable_thread_tracking: 0
2025/07/29-13:52:36.534385 46                  Options.enable_pipelined_write: 0
2025/07/29-13:52:36.534385 46                  Options.unordered_write: 0
2025/07/29-13:52:36.536355 46         Options.allow_concurrent_memtable_write: 1
2025/07/29-13:52:36.536360 46      Options.enable_write_thread_adaptive_yield: 1
2025/07/29-13:52:36.536361 46             Options.write_thread_max_yield_usec: 100
2025/07/29-13:52:36.536362 46            Options.write_thread_slow_yield_usec: 3
2025/07/29-13:52:36.536363 46                               Options.row_cache: None
2025/07/29-13:52:36.536363 46                              Options.wal_filter: None
2025/07/29-13:52:36.536364 46             Options.avoid_flush_during_recovery: 0
2025/07/29-13:52:36.536365 46             Options.allow_ingest_behind: 0
2025/07/29-13:52:36.536365 46             Options.preserve_deletes: 0
2025/07/29-13:52:36.536366 46             Options.two_write_queues: 0
2025/07/29-13:52:36.536366 46             Options.manual_wal_flush: 0
2025/07/29-13:52:36.536366 46             Options.atomic_flush: 0
2025/07/29-13:52:36.536367 46             Options.avoid_unnecessary_blocking_io: 0
2025/07/29-13:52:36.536367 46                 Options.persist_stats_to_disk: 0
2025/07/29-13:52:36.536368 46                 Options.write_dbid_to_manifest: 0
2025/07/29-13:52:36.536368 46                 Options.log_readahead_size: 0
2025/07/29-13:52:36.536369 46                 Options.file_checksum_gen_factory: Unknown
2025/07/29-13:52:36.536369 46                 Options.best_efforts_recovery: 0
2025/07/29-13:52:36.536370 46                Options.max_bgerror_resume_count: 2147483647
2025/07/29-13:52:36.536370 46            Options.bgerror_resume_retry_interval: 1000000
2025/07/29-13:52:36.536371 46             Options.allow_data_in_errors: 0
2025/07/29-13:52:36.536371 46             Options.db_host_id: __hostname__
2025/07/29-13:52:36.536375 46             Options.max_background_jobs: 2
2025/07/29-13:52:36.536375 46             Options.max_background_compactions: -1
2025/07/29-13:52:36.536376 46             Options.max_subcompactions: 1
2025/07/29-13:52:36.536376 46             Options.avoid_flush_during_shutdown: 0
2025/07/29-13:52:36.536377 46           Options.writable_file_max_buffer_size: 1048576
2025/07/29-13:52:36.536377 46             Options.delayed_write_rate : 16777216
2025/07/29-13:52:36.536378 46             Options.max_total_wal_size: 0
2025/07/29-13:52:36.536378 46             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/29-13:52:36.536379 46                   Options.stats_dump_period_sec: 600
2025/07/29-13:52:36.536379 46                 Options.stats_persist_period_sec: 600
2025/07/29-13:52:36.536379 46                 Options.stats_history_buffer_size: 1048576
2025/07/29-13:52:36.536380 46                          Options.max_open_files: -1
2025/07/29-13:52:36.536380 46                          Options.bytes_per_sync: 0
2025/07/29-13:52:36.536381 46                      Options.wal_bytes_per_sync: 0
2025/07/29-13:52:36.536381 46                   Options.strict_bytes_per_sync: 0
2025/07/29-13:52:36.536382 46       Options.compaction_readahead_size: 0
2025/07/29-13:52:36.536382 46                  Options.max_background_flushes: 1
2025/07/29-13:52:36.536383 46 Compression algorithms supported:
2025/07/29-13:52:36.536385 46 	kZSTD supported: 1
2025/07/29-13:52:36.536386 46 	kXpressCompression supported: 0
2025/07/29-13:52:36.536387 46 	kBZip2Compression supported: 0
2025/07/29-13:52:36.536388 46 	kZSTDNotFinalCompression supported: 1
2025/07/29-13:52:36.536389 46 	kLZ4Compression supported: 0
2025/07/29-13:52:36.536389 46 	kZlibCompression supported: 0
2025/07/29-13:52:36.536390 46 	kLZ4HCCompression supported: 0
2025/07/29-13:52:36.536390 46 	kSnappyCompression supported: 0
2025/07/29-13:52:36.536397 46 Fast CRC32 supported: Not supported on x86
2025/07/29-13:52:36.559424 46 [db/db_impl/db_impl_open.cc:307] Creating manifest 1 
2025/07/29-13:52:36.597207 46 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000001
2025/07/29-13:52:36.599136 46 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/07/29-13:52:36.599445 46               Options.comparator: leveldb.BytewiseComparator
2025/07/29-13:52:36.599449 46           Options.merge_operator: None
2025/07/29-13:52:36.599450 46        Options.compaction_filter: None
2025/07/29-13:52:36.599451 46        Options.compaction_filter_factory: None
2025/07/29-13:52:36.599451 46  Options.sst_partitioner_factory: None
2025/07/29-13:52:36.599452 46         Options.memtable_factory: SkipListFactory
2025/07/29-13:52:36.599453 46            Options.table_factory: BlockBasedTable
2025/07/29-13:52:36.599507 46            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7ea3377000c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7ea337640010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 988147875
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/29-13:52:36.599510 46        Options.write_buffer_size: 67108864
2025/07/29-13:52:36.599511 46  Options.max_write_buffer_number: 2
2025/07/29-13:52:36.599513 46        Options.compression[0]: NoCompression
2025/07/29-13:52:36.599514 46        Options.compression[1]: NoCompression
2025/07/29-13:52:36.599515 46        Options.compression[2]: ZSTD
2025/07/29-13:52:36.599515 46        Options.compression[3]: ZSTD
2025/07/29-13:52:36.599516 46        Options.compression[4]: ZSTD
2025/07/29-13:52:36.599516 46                  Options.bottommost_compression: Disabled
2025/07/29-13:52:36.599517 46       Options.prefix_extractor: nullptr
2025/07/29-13:52:36.599517 46   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/29-13:52:36.599518 46             Options.num_levels: 5
2025/07/29-13:52:36.599518 46        Options.min_write_buffer_number_to_merge: 1
2025/07/29-13:52:36.599519 46     Options.max_write_buffer_number_to_maintain: 0
2025/07/29-13:52:36.599519 46     Options.max_write_buffer_size_to_maintain: 0
2025/07/29-13:52:36.599520 46            Options.bottommost_compression_opts.window_bits: -14
2025/07/29-13:52:36.599520 46                  Options.bottommost_compression_opts.level: 32767
2025/07/29-13:52:36.599521 46               Options.bottommost_compression_opts.strategy: 0
2025/07/29-13:52:36.599521 46         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/29-13:52:36.599522 46         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/29-13:52:36.599522 46         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/29-13:52:36.599523 46                  Options.bottommost_compression_opts.enabled: false
2025/07/29-13:52:36.599523 46         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/29-13:52:36.599534 46            Options.compression_opts.window_bits: -14
2025/07/29-13:52:36.599536 46                  Options.compression_opts.level: 32767
2025/07/29-13:52:36.599537 46               Options.compression_opts.strategy: 0
2025/07/29-13:52:36.599537 46         Options.compression_opts.max_dict_bytes: 0
2025/07/29-13:52:36.599538 46         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/29-13:52:36.599538 46         Options.compression_opts.parallel_threads: 1
2025/07/29-13:52:36.599539 46                  Options.compression_opts.enabled: false
2025/07/29-13:52:36.599729 46         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/29-13:52:36.599730 46      Options.level0_file_num_compaction_trigger: 4
2025/07/29-13:52:36.599731 46          Options.level0_slowdown_writes_trigger: 20
2025/07/29-13:52:36.599732 46              Options.level0_stop_writes_trigger: 36
2025/07/29-13:52:36.599733 46                   Options.target_file_size_base: 67108864
2025/07/29-13:52:36.599733 46             Options.target_file_size_multiplier: 2
2025/07/29-13:52:36.599734 46                Options.max_bytes_for_level_base: 268435456
2025/07/29-13:52:36.599734 46 Options.level_compaction_dynamic_level_bytes: 0
2025/07/29-13:52:36.599735 46          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/29-13:52:36.599736 46 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/29-13:52:36.599737 46 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/29-13:52:36.599737 46 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/29-13:52:36.599738 46 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/29-13:52:36.599738 46 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/29-13:52:36.599739 46 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/29-13:52:36.599739 46 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/29-13:52:36.599739 46       Options.max_sequential_skip_in_iterations: 8
2025/07/29-13:52:36.599740 46                    Options.max_compaction_bytes: 1677721600
2025/07/29-13:52:36.599740 46                        Options.arena_block_size: 1048576
2025/07/29-13:52:36.599741 46   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/29-13:52:36.599741 46   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/29-13:52:36.599742 46       Options.rate_limit_delay_max_milliseconds: 100
2025/07/29-13:52:36.599742 46                Options.disable_auto_compactions: 0
2025/07/29-13:52:36.599745 46                        Options.compaction_style: kCompactionStyleLevel
2025/07/29-13:52:36.599746 46                          Options.compaction_pri: kMinOverlappingRatio
2025/07/29-13:52:36.599746 46 Options.compaction_options_universal.size_ratio: 1
2025/07/29-13:52:36.599747 46 Options.compaction_options_universal.min_merge_width: 2
2025/07/29-13:52:36.599747 46 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/29-13:52:36.599748 46 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/29-13:52:36.599748 46 Options.compaction_options_universal.compression_size_percent: -1
2025/07/29-13:52:36.599749 46 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/29-13:52:36.599749 46 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/29-13:52:36.599750 46 Options.compaction_options_fifo.allow_compaction: 0
2025/07/29-13:52:36.599756 46                   Options.table_properties_collectors: 
2025/07/29-13:52:36.599757 46                   Options.inplace_update_support: 0
2025/07/29-13:52:36.599757 46                 Options.inplace_update_num_locks: 10000
2025/07/29-13:52:36.599758 46               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/29-13:52:36.599759 46               Options.memtable_whole_key_filtering: 0
2025/07/29-13:52:36.599759 46   Options.memtable_huge_page_size: 0
2025/07/29-13:52:36.599759 46                           Options.bloom_locality: 0
2025/07/29-13:52:36.599760 46                    Options.max_successive_merges: 0
2025/07/29-13:52:36.599760 46                Options.optimize_filters_for_hits: 0
2025/07/29-13:52:36.599761 46                Options.paranoid_file_checks: 0
2025/07/29-13:52:36.599761 46                Options.force_consistency_checks: 1
2025/07/29-13:52:36.599761 46                Options.report_bg_io_stats: 0
2025/07/29-13:52:36.599762 46                               Options.ttl: 2592000
2025/07/29-13:52:36.599762 46          Options.periodic_compaction_seconds: 0
2025/07/29-13:52:36.599763 46                       Options.enable_blob_files: false
2025/07/29-13:52:36.600089 46                           Options.min_blob_size: 0
2025/07/29-13:52:36.600091 46                          Options.blob_file_size: 268435456
2025/07/29-13:52:36.600092 46                   Options.blob_compression_type: NoCompression
2025/07/29-13:52:36.600093 46          Options.enable_blob_garbage_collection: false
2025/07/29-13:52:36.600093 46      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/29-13:52:36.600094 46 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/29-13:52:36.600095 46          Options.blob_compaction_readahead_size: 0
2025/07/29-13:52:36.603726 46 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/29-13:52:36.603736 46 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 0
2025/07/29-13:52:36.610212 46 [db/version_set.cc:4409] Creating manifest 4
2025/07/29-13:52:36.663917 46 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7ea334d80000
2025/07/29-13:52:36.665477 46 DB pointer 0x7ea334c20000
2025/07/29-13:52:36.666218 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-13:52:36.666230 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7ea337640010#8 capacity: 942.37 MB collections: 1 last_copies: 0 last_secs: 7.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
