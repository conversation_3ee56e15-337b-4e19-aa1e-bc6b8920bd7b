{"nodes": [{"id": "100001", "type": "1", "meta": {"position": {"x": -7, "y": -68}}, "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"type": "string", "name": "input", "required": false}], "trigger_parameters": []}}, {"id": "900001", "type": "2", "meta": {"position": {"x": 1004, "y": -125.6}}, "data": {"nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}, "inputs": {"terminatePlan": "returnVariables", "inputParameters": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "124342", "name": "key0"}, "rawMeta": {"type": 1}}}}]}}}, {"id": "124342", "type": "5", "meta": {"position": {"x": 523, "y": -191.2}}, "data": {"nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "mainColor": "#00B2B2", "subTitle": "代码", "title": "代码"}, "inputs": {"inputParameters": [{"name": "input", "input": {"type": "string", "value": {"type": "literal", "content": "12", "rawMeta": {"type": 1}}}}], "code": "// 在这里，您可以通过 ‘params’  获取节点中的输入变量，并通过 'ret' 输出结果\n// 'params' 和 'ret' 已经被正确地注入到环境中\n// 下面是一个示例，获取节点输入中参数名为‘input’的值：\n// const input = params.input; \n// 下面是一个示例，输出一个包含多种数据类型的 'ret' 对象：\n// const ret = { \"name\": ‘小明’, \"hobbies\": [“看书”, “旅游”] };\n\nasync function main({ params }: Args): Promise<Output> {\n    // 构建输出对象\n    const ret = {\n        \"key0\": params.input + params.input, // 拼接两次入参 input 的值\n        \"key1\": [\"hello\", \"world\"], // 输出一个数组\n        \"key2\": { // 输出一个Object\n            \"key21\": \"hi\"\n        },\n    };\n\n    return ret;\n}", "language": 5, "settingOnError": {"processType": 1, "timeoutMs": 60000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "key0"}, {"type": "list", "name": "key1", "schema": {"type": "string"}}, {"type": "object", "name": "key2", "schema": [{"type": "string", "name": "key21"}]}]}}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "124342"}, {"sourceNodeID": "124342", "targetNodeID": "900001"}], "versions": {"loop": "v2"}}