// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameSpace = "space"

// Space Space Table
type Space struct {
	ID          int64          `gorm:"column:id;primaryKey;autoIncrement:true;comment:Primary Key ID, Space ID" json:"id"`                     // Primary Key ID, Space ID
	OwnerID     int64          `gorm:"column:owner_id;not null;comment:Owner ID" json:"owner_id"`                                              // Owner ID
	Name        string         `gorm:"column:name;not null;comment:Space Name" json:"name"`                                                    // Space Name
	Description string         `gorm:"column:description;not null;comment:Space Description" json:"description"`                               // Space Description
	IconURI     string         `gorm:"column:icon_uri;not null;comment:Icon URI" json:"icon_uri"`                                              // Icon URI
	CreatorID   int64          `gorm:"column:creator_id;not null;comment:Creator ID" json:"creator_id"`                                        // Creator ID
	CreatedAt   int64          `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Creation Time (Milliseconds)" json:"created_at"` // Creation Time (Milliseconds)
	UpdatedAt   int64          `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time (Milliseconds)" json:"updated_at"`   // Update Time (Milliseconds)
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;comment:Deletion Time (Milliseconds)" json:"deleted_at"`                               // Deletion Time (Milliseconds)
}

// TableName Space's table name
func (*Space) TableName() string {
	return TableNameSpace
}
