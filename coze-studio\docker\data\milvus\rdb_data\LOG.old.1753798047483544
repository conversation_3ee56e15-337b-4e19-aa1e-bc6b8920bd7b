2025/07/29-14:02:24.275839 35 RocksDB version: 6.29.5
2025/07/29-14:02:24.276250 35 Git sha 0
2025/07/29-14:02:24.276254 35 Compile date 2024-11-15 11:22:58
2025/07/29-14:02:24.276256 35 DB SUMMARY
2025/07/29-14:02:24.276257 35 DB Session ID:  ZG4TB7E5QFERH67TF43J
2025/07/29-14:02:24.279665 35 CURRENT file:  CURRENT
2025/07/29-14:02:24.279676 35 IDENTITY file:  IDENTITY
2025/07/29-14:02:24.280302 35 MANIFEST file:  MANIFEST-000004 size: 116 Bytes
2025/07/29-14:02:24.280309 35 SST files in /var/lib/milvus/rdb_data dir, Total Num: 0, files: 
2025/07/29-14:02:24.280311 35 Write Ahead Log file in /var/lib/milvus/rdb_data: 000005.log size: 0 ; 
2025/07/29-14:02:24.280313 35                         Options.error_if_exists: 0
2025/07/29-14:02:24.280314 35                       Options.create_if_missing: 1
2025/07/29-14:02:24.280314 35                         Options.paranoid_checks: 1
2025/07/29-14:02:24.280315 35             Options.flush_verify_memtable_count: 1
2025/07/29-14:02:24.280315 35                               Options.track_and_verify_wals_in_manifest: 0
2025/07/29-14:02:24.280316 35                                     Options.env: 0x7d35335f9d00
2025/07/29-14:02:24.280317 35                                      Options.fs: PosixFileSystem
2025/07/29-14:02:24.280318 35                                Options.info_log: 0x7d3456290140
2025/07/29-14:02:24.280318 35                Options.max_file_opening_threads: 16
2025/07/29-14:02:24.280319 35                              Options.statistics: (nil)
2025/07/29-14:02:24.280319 35                               Options.use_fsync: 0
2025/07/29-14:02:24.280320 35                       Options.max_log_file_size: 0
2025/07/29-14:02:24.280320 35                  Options.max_manifest_file_size: 1073741824
2025/07/29-14:02:24.280321 35                   Options.log_file_time_to_roll: 0
2025/07/29-14:02:24.280321 35                       Options.keep_log_file_num: 1000
2025/07/29-14:02:24.280322 35                    Options.recycle_log_file_num: 0
2025/07/29-14:02:24.280322 35                         Options.allow_fallocate: 1
2025/07/29-14:02:24.280323 35                        Options.allow_mmap_reads: 0
2025/07/29-14:02:24.280323 35                       Options.allow_mmap_writes: 0
2025/07/29-14:02:24.280324 35                        Options.use_direct_reads: 0
2025/07/29-14:02:24.280324 35                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/29-14:02:24.280325 35          Options.create_missing_column_families: 1
2025/07/29-14:02:24.280325 35                              Options.db_log_dir: 
2025/07/29-14:02:24.280326 35                                 Options.wal_dir: 
2025/07/29-14:02:24.280326 35                Options.table_cache_numshardbits: 6
2025/07/29-14:02:24.280327 35                         Options.WAL_ttl_seconds: 0
2025/07/29-14:02:24.280327 35                       Options.WAL_size_limit_MB: 0
2025/07/29-14:02:24.280328 35                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/29-14:02:24.280328 35             Options.manifest_preallocation_size: 4194304
2025/07/29-14:02:24.280329 35                     Options.is_fd_close_on_exec: 1
2025/07/29-14:02:24.280329 35                   Options.advise_random_on_open: 1
2025/07/29-14:02:24.280329 35                   Options.experimental_mempurge_threshold: 0.000000
2025/07/29-14:02:24.280334 35                    Options.db_write_buffer_size: 0
2025/07/29-14:02:24.280334 35                    Options.write_buffer_manager: 0x7d345ec60280
2025/07/29-14:02:24.280335 35         Options.access_hint_on_compaction_start: 1
2025/07/29-14:02:24.280335 35  Options.new_table_reader_for_compaction_inputs: 0
2025/07/29-14:02:24.280336 35           Options.random_access_max_buffer_size: 1048576
2025/07/29-14:02:24.280336 35                      Options.use_adaptive_mutex: 0
2025/07/29-14:02:24.280336 35                            Options.rate_limiter: (nil)
2025/07/29-14:02:24.280338 35     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/29-14:02:24.280339 35                       Options.wal_recovery_mode: 2
2025/07/29-14:02:24.280339 35                  Options.enable_thread_tracking: 0
2025/07/29-14:02:24.280656 35                  Options.enable_pipelined_write: 0
2025/07/29-14:02:24.280660 35                  Options.unordered_write: 0
2025/07/29-14:02:24.280660 35         Options.allow_concurrent_memtable_write: 1
2025/07/29-14:02:24.280661 35      Options.enable_write_thread_adaptive_yield: 1
2025/07/29-14:02:24.280661 35             Options.write_thread_max_yield_usec: 100
2025/07/29-14:02:24.280662 35            Options.write_thread_slow_yield_usec: 3
2025/07/29-14:02:24.280662 35                               Options.row_cache: None
2025/07/29-14:02:24.280663 35                              Options.wal_filter: None
2025/07/29-14:02:24.280664 35             Options.avoid_flush_during_recovery: 0
2025/07/29-14:02:24.280664 35             Options.allow_ingest_behind: 0
2025/07/29-14:02:24.280665 35             Options.preserve_deletes: 0
2025/07/29-14:02:24.280665 35             Options.two_write_queues: 0
2025/07/29-14:02:24.280666 35             Options.manual_wal_flush: 0
2025/07/29-14:02:24.280666 35             Options.atomic_flush: 0
2025/07/29-14:02:24.280667 35             Options.avoid_unnecessary_blocking_io: 0
2025/07/29-14:02:24.280667 35                 Options.persist_stats_to_disk: 0
2025/07/29-14:02:24.280667 35                 Options.write_dbid_to_manifest: 0
2025/07/29-14:02:24.280668 35                 Options.log_readahead_size: 0
2025/07/29-14:02:24.280668 35                 Options.file_checksum_gen_factory: Unknown
2025/07/29-14:02:24.280669 35                 Options.best_efforts_recovery: 0
2025/07/29-14:02:24.280670 35                Options.max_bgerror_resume_count: 2147483647
2025/07/29-14:02:24.280670 35            Options.bgerror_resume_retry_interval: 1000000
2025/07/29-14:02:24.280671 35             Options.allow_data_in_errors: 0
2025/07/29-14:02:24.280671 35             Options.db_host_id: __hostname__
2025/07/29-14:02:24.280672 35             Options.max_background_jobs: 2
2025/07/29-14:02:24.280673 35             Options.max_background_compactions: -1
2025/07/29-14:02:24.280673 35             Options.max_subcompactions: 1
2025/07/29-14:02:24.280674 35             Options.avoid_flush_during_shutdown: 0
2025/07/29-14:02:24.280674 35           Options.writable_file_max_buffer_size: 1048576
2025/07/29-14:02:24.280674 35             Options.delayed_write_rate : 16777216
2025/07/29-14:02:24.280675 35             Options.max_total_wal_size: 0
2025/07/29-14:02:24.280675 35             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/29-14:02:24.280676 35                   Options.stats_dump_period_sec: 600
2025/07/29-14:02:24.280676 35                 Options.stats_persist_period_sec: 600
2025/07/29-14:02:24.280677 35                 Options.stats_history_buffer_size: 1048576
2025/07/29-14:02:24.280677 35                          Options.max_open_files: -1
2025/07/29-14:02:24.280678 35                          Options.bytes_per_sync: 0
2025/07/29-14:02:24.280678 35                      Options.wal_bytes_per_sync: 0
2025/07/29-14:02:24.280679 35                   Options.strict_bytes_per_sync: 0
2025/07/29-14:02:24.280679 35       Options.compaction_readahead_size: 0
2025/07/29-14:02:24.280680 35                  Options.max_background_flushes: 1
2025/07/29-14:02:24.280680 35 Compression algorithms supported:
2025/07/29-14:02:24.280683 35 	kZSTD supported: 1
2025/07/29-14:02:24.280684 35 	kXpressCompression supported: 0
2025/07/29-14:02:24.280685 35 	kBZip2Compression supported: 0
2025/07/29-14:02:24.280685 35 	kZSTDNotFinalCompression supported: 1
2025/07/29-14:02:24.280686 35 	kLZ4Compression supported: 0
2025/07/29-14:02:24.280687 35 	kZlibCompression supported: 0
2025/07/29-14:02:24.280687 35 	kLZ4HCCompression supported: 0
2025/07/29-14:02:24.280688 35 	kSnappyCompression supported: 0
2025/07/29-14:02:24.280691 35 Fast CRC32 supported: Not supported on x86
2025/07/29-14:02:24.285474 35 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000004
2025/07/29-14:02:24.286672 35 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/07/29-14:02:24.286932 35               Options.comparator: leveldb.BytewiseComparator
2025/07/29-14:02:24.286936 35           Options.merge_operator: None
2025/07/29-14:02:24.286937 35        Options.compaction_filter: None
2025/07/29-14:02:24.286937 35        Options.compaction_filter_factory: None
2025/07/29-14:02:24.286938 35  Options.sst_partitioner_factory: None
2025/07/29-14:02:24.286939 35         Options.memtable_factory: SkipListFactory
2025/07/29-14:02:24.286939 35            Options.table_factory: BlockBasedTable
2025/07/29-14:02:24.286955 35            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7d345ec01340)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7d345ec60010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 988147875
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/29-14:02:24.286956 35        Options.write_buffer_size: 67108864
2025/07/29-14:02:24.286957 35  Options.max_write_buffer_number: 2
2025/07/29-14:02:24.286957 35        Options.compression[0]: NoCompression
2025/07/29-14:02:24.286958 35        Options.compression[1]: NoCompression
2025/07/29-14:02:24.286959 35        Options.compression[2]: ZSTD
2025/07/29-14:02:24.286959 35        Options.compression[3]: ZSTD
2025/07/29-14:02:24.286960 35        Options.compression[4]: ZSTD
2025/07/29-14:02:24.286960 35                  Options.bottommost_compression: Disabled
2025/07/29-14:02:24.286961 35       Options.prefix_extractor: nullptr
2025/07/29-14:02:24.286961 35   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/29-14:02:24.286961 35             Options.num_levels: 5
2025/07/29-14:02:24.286962 35        Options.min_write_buffer_number_to_merge: 1
2025/07/29-14:02:24.286962 35     Options.max_write_buffer_number_to_maintain: 0
2025/07/29-14:02:24.286963 35     Options.max_write_buffer_size_to_maintain: 0
2025/07/29-14:02:24.286963 35            Options.bottommost_compression_opts.window_bits: -14
2025/07/29-14:02:24.286964 35                  Options.bottommost_compression_opts.level: 32767
2025/07/29-14:02:24.286964 35               Options.bottommost_compression_opts.strategy: 0
2025/07/29-14:02:24.286965 35         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/29-14:02:24.286965 35         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/29-14:02:24.286966 35         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/29-14:02:24.286966 35                  Options.bottommost_compression_opts.enabled: false
2025/07/29-14:02:24.286967 35         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/29-14:02:24.286967 35            Options.compression_opts.window_bits: -14
2025/07/29-14:02:24.286968 35                  Options.compression_opts.level: 32767
2025/07/29-14:02:24.286968 35               Options.compression_opts.strategy: 0
2025/07/29-14:02:24.286969 35         Options.compression_opts.max_dict_bytes: 0
2025/07/29-14:02:24.286970 35         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/29-14:02:24.286970 35         Options.compression_opts.parallel_threads: 1
2025/07/29-14:02:24.287185 35                  Options.compression_opts.enabled: false
2025/07/29-14:02:24.287189 35         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/29-14:02:24.287189 35      Options.level0_file_num_compaction_trigger: 4
2025/07/29-14:02:24.287190 35          Options.level0_slowdown_writes_trigger: 20
2025/07/29-14:02:24.287190 35              Options.level0_stop_writes_trigger: 36
2025/07/29-14:02:24.287191 35                   Options.target_file_size_base: 67108864
2025/07/29-14:02:24.287191 35             Options.target_file_size_multiplier: 2
2025/07/29-14:02:24.287192 35                Options.max_bytes_for_level_base: 268435456
2025/07/29-14:02:24.287192 35 Options.level_compaction_dynamic_level_bytes: 0
2025/07/29-14:02:24.287193 35          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/29-14:02:24.287196 35 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/29-14:02:24.287196 35 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/29-14:02:24.287197 35 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/29-14:02:24.287198 35 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/29-14:02:24.287198 35 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/29-14:02:24.287198 35 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/29-14:02:24.287199 35 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/29-14:02:24.287199 35       Options.max_sequential_skip_in_iterations: 8
2025/07/29-14:02:24.287200 35                    Options.max_compaction_bytes: 1677721600
2025/07/29-14:02:24.287200 35                        Options.arena_block_size: 1048576
2025/07/29-14:02:24.287201 35   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/29-14:02:24.287201 35   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/29-14:02:24.287202 35       Options.rate_limit_delay_max_milliseconds: 100
2025/07/29-14:02:24.287202 35                Options.disable_auto_compactions: 0
2025/07/29-14:02:24.287206 35                        Options.compaction_style: kCompactionStyleLevel
2025/07/29-14:02:24.287207 35                          Options.compaction_pri: kMinOverlappingRatio
2025/07/29-14:02:24.287208 35 Options.compaction_options_universal.size_ratio: 1
2025/07/29-14:02:24.287208 35 Options.compaction_options_universal.min_merge_width: 2
2025/07/29-14:02:24.287208 35 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/29-14:02:24.287209 35 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/29-14:02:24.287209 35 Options.compaction_options_universal.compression_size_percent: -1
2025/07/29-14:02:24.287210 35 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/29-14:02:24.287211 35 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/29-14:02:24.287211 35 Options.compaction_options_fifo.allow_compaction: 0
2025/07/29-14:02:24.287218 35                   Options.table_properties_collectors: 
2025/07/29-14:02:24.287218 35                   Options.inplace_update_support: 0
2025/07/29-14:02:24.287219 35                 Options.inplace_update_num_locks: 10000
2025/07/29-14:02:24.287219 35               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/29-14:02:24.287220 35               Options.memtable_whole_key_filtering: 0
2025/07/29-14:02:24.287220 35   Options.memtable_huge_page_size: 0
2025/07/29-14:02:24.287221 35                           Options.bloom_locality: 0
2025/07/29-14:02:24.287221 35                    Options.max_successive_merges: 0
2025/07/29-14:02:24.287221 35                Options.optimize_filters_for_hits: 0
2025/07/29-14:02:24.287222 35                Options.paranoid_file_checks: 0
2025/07/29-14:02:24.287222 35                Options.force_consistency_checks: 1
2025/07/29-14:02:24.287223 35                Options.report_bg_io_stats: 0
2025/07/29-14:02:24.287223 35                               Options.ttl: 2592000
2025/07/29-14:02:24.287224 35          Options.periodic_compaction_seconds: 0
2025/07/29-14:02:24.287555 35                       Options.enable_blob_files: false
2025/07/29-14:02:24.287557 35                           Options.min_blob_size: 0
2025/07/29-14:02:24.287558 35                          Options.blob_file_size: 268435456
2025/07/29-14:02:24.287559 35                   Options.blob_compression_type: NoCompression
2025/07/29-14:02:24.287560 35          Options.enable_blob_garbage_collection: false
2025/07/29-14:02:24.287560 35      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/29-14:02:24.287561 35 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/29-14:02:24.287562 35          Options.blob_compaction_readahead_size: 0
2025/07/29-14:02:24.288378 35 [db/column_family.cc:605] --------------- Options for column family [properties]:
2025/07/29-14:02:24.288381 35               Options.comparator: leveldb.BytewiseComparator
2025/07/29-14:02:24.288382 35           Options.merge_operator: None
2025/07/29-14:02:24.288383 35        Options.compaction_filter: None
2025/07/29-14:02:24.288383 35        Options.compaction_filter_factory: None
2025/07/29-14:02:24.288384 35  Options.sst_partitioner_factory: None
2025/07/29-14:02:24.288384 35         Options.memtable_factory: SkipListFactory
2025/07/29-14:02:24.288385 35            Options.table_factory: BlockBasedTable
2025/07/29-14:02:24.288394 35            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7d345ec01340)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7d345ec60010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 988147875
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/29-14:02:24.288394 35        Options.write_buffer_size: 67108864
2025/07/29-14:02:24.288395 35  Options.max_write_buffer_number: 2
2025/07/29-14:02:24.288396 35        Options.compression[0]: NoCompression
2025/07/29-14:02:24.288396 35        Options.compression[1]: NoCompression
2025/07/29-14:02:24.288397 35        Options.compression[2]: ZSTD
2025/07/29-14:02:24.288397 35        Options.compression[3]: ZSTD
2025/07/29-14:02:24.288398 35        Options.compression[4]: ZSTD
2025/07/29-14:02:24.288398 35                  Options.bottommost_compression: Disabled
2025/07/29-14:02:24.288399 35       Options.prefix_extractor: nullptr
2025/07/29-14:02:24.288399 35   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/29-14:02:24.288400 35             Options.num_levels: 5
2025/07/29-14:02:24.288400 35        Options.min_write_buffer_number_to_merge: 1
2025/07/29-14:02:24.288401 35     Options.max_write_buffer_number_to_maintain: 0
2025/07/29-14:02:24.288401 35     Options.max_write_buffer_size_to_maintain: 0
2025/07/29-14:02:24.288402 35            Options.bottommost_compression_opts.window_bits: -14
2025/07/29-14:02:24.288402 35                  Options.bottommost_compression_opts.level: 32767
2025/07/29-14:02:24.288403 35               Options.bottommost_compression_opts.strategy: 0
2025/07/29-14:02:24.288403 35         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/29-14:02:24.288404 35         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/29-14:02:24.288404 35         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/29-14:02:24.288587 35                  Options.bottommost_compression_opts.enabled: false
2025/07/29-14:02:24.288588 35         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/29-14:02:24.288589 35            Options.compression_opts.window_bits: -14
2025/07/29-14:02:24.288589 35                  Options.compression_opts.level: 32767
2025/07/29-14:02:24.288590 35               Options.compression_opts.strategy: 0
2025/07/29-14:02:24.288590 35         Options.compression_opts.max_dict_bytes: 0
2025/07/29-14:02:24.288591 35         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/29-14:02:24.288591 35         Options.compression_opts.parallel_threads: 1
2025/07/29-14:02:24.288591 35                  Options.compression_opts.enabled: false
2025/07/29-14:02:24.288592 35         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/29-14:02:24.288592 35      Options.level0_file_num_compaction_trigger: 4
2025/07/29-14:02:24.288593 35          Options.level0_slowdown_writes_trigger: 20
2025/07/29-14:02:24.288593 35              Options.level0_stop_writes_trigger: 36
2025/07/29-14:02:24.288594 35                   Options.target_file_size_base: 67108864
2025/07/29-14:02:24.288594 35             Options.target_file_size_multiplier: 2
2025/07/29-14:02:24.288594 35                Options.max_bytes_for_level_base: 268435456
2025/07/29-14:02:24.288595 35 Options.level_compaction_dynamic_level_bytes: 0
2025/07/29-14:02:24.288595 35          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/29-14:02:24.288596 35 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/29-14:02:24.288597 35 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/29-14:02:24.288598 35 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/29-14:02:24.288598 35 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/29-14:02:24.288598 35 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/29-14:02:24.288599 35 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/29-14:02:24.288599 35 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/29-14:02:24.288600 35       Options.max_sequential_skip_in_iterations: 8
2025/07/29-14:02:24.288600 35                    Options.max_compaction_bytes: 1677721600
2025/07/29-14:02:24.288600 35                        Options.arena_block_size: 1048576
2025/07/29-14:02:24.288601 35   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/29-14:02:24.288601 35   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/29-14:02:24.288602 35       Options.rate_limit_delay_max_milliseconds: 100
2025/07/29-14:02:24.288602 35                Options.disable_auto_compactions: 0
2025/07/29-14:02:24.288604 35                        Options.compaction_style: kCompactionStyleLevel
2025/07/29-14:02:24.288604 35                          Options.compaction_pri: kMinOverlappingRatio
2025/07/29-14:02:24.288605 35 Options.compaction_options_universal.size_ratio: 1
2025/07/29-14:02:24.288605 35 Options.compaction_options_universal.min_merge_width: 2
2025/07/29-14:02:24.288605 35 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/29-14:02:24.288606 35 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/29-14:02:24.288606 35 Options.compaction_options_universal.compression_size_percent: -1
2025/07/29-14:02:24.288607 35 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/29-14:02:24.288607 35 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/29-14:02:24.288608 35 Options.compaction_options_fifo.allow_compaction: 0
2025/07/29-14:02:24.288610 35                   Options.table_properties_collectors: 
2025/07/29-14:02:24.288610 35                   Options.inplace_update_support: 0
2025/07/29-14:02:24.288610 35                 Options.inplace_update_num_locks: 10000
2025/07/29-14:02:24.288611 35               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/29-14:02:24.288612 35               Options.memtable_whole_key_filtering: 0
2025/07/29-14:02:24.288770 35   Options.memtable_huge_page_size: 0
2025/07/29-14:02:24.288772 35                           Options.bloom_locality: 0
2025/07/29-14:02:24.288772 35                    Options.max_successive_merges: 0
2025/07/29-14:02:24.288773 35                Options.optimize_filters_for_hits: 0
2025/07/29-14:02:24.288774 35                Options.paranoid_file_checks: 0
2025/07/29-14:02:24.288774 35                Options.force_consistency_checks: 1
2025/07/29-14:02:24.288775 35                Options.report_bg_io_stats: 0
2025/07/29-14:02:24.288775 35                               Options.ttl: 2592000
2025/07/29-14:02:24.288776 35          Options.periodic_compaction_seconds: 0
2025/07/29-14:02:24.288776 35                       Options.enable_blob_files: false
2025/07/29-14:02:24.288777 35                           Options.min_blob_size: 0
2025/07/29-14:02:24.288777 35                          Options.blob_file_size: 268435456
2025/07/29-14:02:24.288778 35                   Options.blob_compression_type: NoCompression
2025/07/29-14:02:24.288778 35          Options.enable_blob_garbage_collection: false
2025/07/29-14:02:24.288779 35      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/29-14:02:24.288780 35 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/29-14:02:24.288780 35          Options.blob_compaction_readahead_size: 0
2025/07/29-14:02:24.291135 35 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000004 succeeded,manifest_file_number is 4, next_file_number is 7, last_sequence is 0, log_number is 5,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 0
2025/07/29-14:02:24.291139 35 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 0
2025/07/29-14:02:24.291140 35 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 5
2025/07/29-14:02:24.294656 35 [db/version_set.cc:4409] Creating manifest 10
2025/07/29-14:02:24.314836 35 EVENT_LOG_v1 {"time_micros": 1753797744314813, "job": 1, "event": "recovery_started", "wal_files": [5]}
2025/07/29-14:02:24.314844 35 [db/db_impl/db_impl_open.cc:888] Recovering log #5 mode 2
2025/07/29-14:02:24.316121 35 [db/version_set.cc:4409] Creating manifest 11
2025/07/29-14:02:24.339568 35 EVENT_LOG_v1 {"time_micros": 1753797744339560, "job": 1, "event": "recovery_finished"}
2025/07/29-14:02:24.363301 35 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7d3456390700
2025/07/29-14:02:24.364921 35 DB pointer 0x7d3456221c00
2025/07/29-14:02:27.365560 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-14:02:27.365586 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.1 total, 3.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.1 total, 3.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7d345ec60010#8 capacity: 942.37 MB collections: 1 last_copies: 2 last_secs: 5.6e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.1 total, 3.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7d345ec60010#8 capacity: 942.37 MB collections: 1 last_copies: 2 last_secs: 5.6e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
