2025/07/29-14:02:24.139231 35 RocksDB version: 6.29.5
2025/07/29-14:02:24.139675 35 Git sha 0
2025/07/29-14:02:24.139688 35 Compile date 2024-11-15 11:22:58
2025/07/29-14:02:24.139699 35 DB SUMMARY
2025/07/29-14:02:24.139700 35 DB Session ID:  ZG4TB7E5QFERH67TF43I
2025/07/29-14:02:24.141303 35 CURRENT file:  CURRENT
2025/07/29-14:02:24.141308 35 IDENTITY file:  IDENTITY
2025/07/29-14:02:24.141927 35 MANIFEST file:  MANIFEST-000004 size: 59 Bytes
2025/07/29-14:02:24.141933 35 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 0, files: 
2025/07/29-14:02:24.141938 35 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000005.log size: 1742 ; 
2025/07/29-14:02:24.141942 35                         Options.error_if_exists: 0
2025/07/29-14:02:24.141943 35                       Options.create_if_missing: 1
2025/07/29-14:02:24.141943 35                         Options.paranoid_checks: 1
2025/07/29-14:02:24.141944 35             Options.flush_verify_memtable_count: 1
2025/07/29-14:02:24.141945 35                               Options.track_and_verify_wals_in_manifest: 0
2025/07/29-14:02:24.141945 35                                     Options.env: 0x7d35335f9d00
2025/07/29-14:02:24.141946 35                                      Options.fs: PosixFileSystem
2025/07/29-14:02:24.141947 35                                Options.info_log: 0x7d3456290050
2025/07/29-14:02:24.141947 35                Options.max_file_opening_threads: 16
2025/07/29-14:02:24.141948 35                              Options.statistics: (nil)
2025/07/29-14:02:24.141949 35                               Options.use_fsync: 0
2025/07/29-14:02:24.141949 35                       Options.max_log_file_size: 0
2025/07/29-14:02:24.141950 35                  Options.max_manifest_file_size: 1073741824
2025/07/29-14:02:24.141950 35                   Options.log_file_time_to_roll: 0
2025/07/29-14:02:24.141951 35                       Options.keep_log_file_num: 1000
2025/07/29-14:02:24.141952 35                    Options.recycle_log_file_num: 0
2025/07/29-14:02:24.141952 35                         Options.allow_fallocate: 1
2025/07/29-14:02:24.141953 35                        Options.allow_mmap_reads: 0
2025/07/29-14:02:24.141953 35                       Options.allow_mmap_writes: 0
2025/07/29-14:02:24.141954 35                        Options.use_direct_reads: 0
2025/07/29-14:02:24.141954 35                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/29-14:02:24.141955 35          Options.create_missing_column_families: 0
2025/07/29-14:02:24.141955 35                              Options.db_log_dir: 
2025/07/29-14:02:24.141956 35                                 Options.wal_dir: 
2025/07/29-14:02:24.141956 35                Options.table_cache_numshardbits: 6
2025/07/29-14:02:24.141957 35                         Options.WAL_ttl_seconds: 0
2025/07/29-14:02:24.141957 35                       Options.WAL_size_limit_MB: 0
2025/07/29-14:02:24.141958 35                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/29-14:02:24.141958 35             Options.manifest_preallocation_size: 4194304
2025/07/29-14:02:24.141959 35                     Options.is_fd_close_on_exec: 1
2025/07/29-14:02:24.141960 35                   Options.advise_random_on_open: 1
2025/07/29-14:02:24.141960 35                   Options.experimental_mempurge_threshold: 0.000000
2025/07/29-14:02:24.141973 35                    Options.db_write_buffer_size: 0
2025/07/29-14:02:24.141973 35                    Options.write_buffer_manager: 0x7d345ec600a0
2025/07/29-14:02:24.141974 35         Options.access_hint_on_compaction_start: 1
2025/07/29-14:02:24.141974 35  Options.new_table_reader_for_compaction_inputs: 0
2025/07/29-14:02:24.141975 35           Options.random_access_max_buffer_size: 1048576
2025/07/29-14:02:24.141975 35                      Options.use_adaptive_mutex: 0
2025/07/29-14:02:24.141976 35                            Options.rate_limiter: (nil)
2025/07/29-14:02:24.141980 35     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/29-14:02:24.141980 35                       Options.wal_recovery_mode: 2
2025/07/29-14:02:24.142631 35                  Options.enable_thread_tracking: 0
2025/07/29-14:02:24.142636 35                  Options.enable_pipelined_write: 0
2025/07/29-14:02:24.142637 35                  Options.unordered_write: 0
2025/07/29-14:02:24.142637 35         Options.allow_concurrent_memtable_write: 1
2025/07/29-14:02:24.142638 35      Options.enable_write_thread_adaptive_yield: 1
2025/07/29-14:02:24.142638 35             Options.write_thread_max_yield_usec: 100
2025/07/29-14:02:24.142639 35            Options.write_thread_slow_yield_usec: 3
2025/07/29-14:02:24.142639 35                               Options.row_cache: None
2025/07/29-14:02:24.142640 35                              Options.wal_filter: None
2025/07/29-14:02:24.142640 35             Options.avoid_flush_during_recovery: 0
2025/07/29-14:02:24.142641 35             Options.allow_ingest_behind: 0
2025/07/29-14:02:24.142641 35             Options.preserve_deletes: 0
2025/07/29-14:02:24.142642 35             Options.two_write_queues: 0
2025/07/29-14:02:24.142642 35             Options.manual_wal_flush: 0
2025/07/29-14:02:24.142642 35             Options.atomic_flush: 0
2025/07/29-14:02:24.142643 35             Options.avoid_unnecessary_blocking_io: 0
2025/07/29-14:02:24.142643 35                 Options.persist_stats_to_disk: 0
2025/07/29-14:02:24.142644 35                 Options.write_dbid_to_manifest: 0
2025/07/29-14:02:24.142644 35                 Options.log_readahead_size: 0
2025/07/29-14:02:24.142645 35                 Options.file_checksum_gen_factory: Unknown
2025/07/29-14:02:24.142645 35                 Options.best_efforts_recovery: 0
2025/07/29-14:02:24.142646 35                Options.max_bgerror_resume_count: 2147483647
2025/07/29-14:02:24.142646 35            Options.bgerror_resume_retry_interval: 1000000
2025/07/29-14:02:24.142647 35             Options.allow_data_in_errors: 0
2025/07/29-14:02:24.142647 35             Options.db_host_id: __hostname__
2025/07/29-14:02:24.142650 35             Options.max_background_jobs: 2
2025/07/29-14:02:24.142650 35             Options.max_background_compactions: -1
2025/07/29-14:02:24.142651 35             Options.max_subcompactions: 1
2025/07/29-14:02:24.142651 35             Options.avoid_flush_during_shutdown: 0
2025/07/29-14:02:24.142652 35           Options.writable_file_max_buffer_size: 1048576
2025/07/29-14:02:24.142652 35             Options.delayed_write_rate : 16777216
2025/07/29-14:02:24.142652 35             Options.max_total_wal_size: 0
2025/07/29-14:02:24.142653 35             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/29-14:02:24.142653 35                   Options.stats_dump_period_sec: 600
2025/07/29-14:02:24.142654 35                 Options.stats_persist_period_sec: 600
2025/07/29-14:02:24.142654 35                 Options.stats_history_buffer_size: 1048576
2025/07/29-14:02:24.142655 35                          Options.max_open_files: -1
2025/07/29-14:02:24.142655 35                          Options.bytes_per_sync: 0
2025/07/29-14:02:24.142656 35                      Options.wal_bytes_per_sync: 0
2025/07/29-14:02:24.142656 35                   Options.strict_bytes_per_sync: 0
2025/07/29-14:02:24.142657 35       Options.compaction_readahead_size: 0
2025/07/29-14:02:24.142657 35                  Options.max_background_flushes: 1
2025/07/29-14:02:24.142657 35 Compression algorithms supported:
2025/07/29-14:02:24.142660 35 	kZSTD supported: 1
2025/07/29-14:02:24.142660 35 	kXpressCompression supported: 0
2025/07/29-14:02:24.142662 35 	kBZip2Compression supported: 0
2025/07/29-14:02:24.142662 35 	kZSTDNotFinalCompression supported: 1
2025/07/29-14:02:24.142663 35 	kLZ4Compression supported: 0
2025/07/29-14:02:24.142663 35 	kZlibCompression supported: 0
2025/07/29-14:02:24.142664 35 	kLZ4HCCompression supported: 0
2025/07/29-14:02:24.142664 35 	kSnappyCompression supported: 0
2025/07/29-14:02:24.142669 35 Fast CRC32 supported: Not supported on x86
2025/07/29-14:02:24.148393 35 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000004
2025/07/29-14:02:24.150218 35 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/07/29-14:02:24.150225 35               Options.comparator: leveldb.BytewiseComparator
2025/07/29-14:02:24.150227 35           Options.merge_operator: None
2025/07/29-14:02:24.150228 35        Options.compaction_filter: None
2025/07/29-14:02:24.150228 35        Options.compaction_filter_factory: None
2025/07/29-14:02:24.150228 35  Options.sst_partitioner_factory: None
2025/07/29-14:02:24.150229 35         Options.memtable_factory: SkipListFactory
2025/07/29-14:02:24.150230 35            Options.table_factory: BlockBasedTable
2025/07/29-14:02:24.150265 35            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7d345ec000e0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7d345ec60010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 988147875
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/29-14:02:24.150270 35        Options.write_buffer_size: 67108864
2025/07/29-14:02:24.150270 35  Options.max_write_buffer_number: 2
2025/07/29-14:02:24.150272 35        Options.compression[0]: NoCompression
2025/07/29-14:02:24.150273 35        Options.compression[1]: NoCompression
2025/07/29-14:02:24.150274 35        Options.compression[2]: ZSTD
2025/07/29-14:02:24.150274 35        Options.compression[3]: ZSTD
2025/07/29-14:02:24.150274 35        Options.compression[4]: ZSTD
2025/07/29-14:02:24.150275 35                  Options.bottommost_compression: Disabled
2025/07/29-14:02:24.150275 35       Options.prefix_extractor: nullptr
2025/07/29-14:02:24.150276 35   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/29-14:02:24.150276 35             Options.num_levels: 5
2025/07/29-14:02:24.150277 35        Options.min_write_buffer_number_to_merge: 1
2025/07/29-14:02:24.150277 35     Options.max_write_buffer_number_to_maintain: 0
2025/07/29-14:02:24.150278 35     Options.max_write_buffer_size_to_maintain: 0
2025/07/29-14:02:24.150278 35            Options.bottommost_compression_opts.window_bits: -14
2025/07/29-14:02:24.150279 35                  Options.bottommost_compression_opts.level: 32767
2025/07/29-14:02:24.150279 35               Options.bottommost_compression_opts.strategy: 0
2025/07/29-14:02:24.150280 35         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/29-14:02:24.150280 35         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/29-14:02:24.150280 35         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/29-14:02:24.150281 35                  Options.bottommost_compression_opts.enabled: false
2025/07/29-14:02:24.150282 35         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/29-14:02:24.150383 35            Options.compression_opts.window_bits: -14
2025/07/29-14:02:24.150385 35                  Options.compression_opts.level: 32767
2025/07/29-14:02:24.150386 35               Options.compression_opts.strategy: 0
2025/07/29-14:02:24.150386 35         Options.compression_opts.max_dict_bytes: 0
2025/07/29-14:02:24.150387 35         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/29-14:02:24.150387 35         Options.compression_opts.parallel_threads: 1
2025/07/29-14:02:24.150581 35                  Options.compression_opts.enabled: false
2025/07/29-14:02:24.150586 35         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/29-14:02:24.150587 35      Options.level0_file_num_compaction_trigger: 4
2025/07/29-14:02:24.150588 35          Options.level0_slowdown_writes_trigger: 20
2025/07/29-14:02:24.150589 35              Options.level0_stop_writes_trigger: 36
2025/07/29-14:02:24.150589 35                   Options.target_file_size_base: 67108864
2025/07/29-14:02:24.150590 35             Options.target_file_size_multiplier: 2
2025/07/29-14:02:24.150590 35                Options.max_bytes_for_level_base: 268435456
2025/07/29-14:02:24.150591 35 Options.level_compaction_dynamic_level_bytes: 0
2025/07/29-14:02:24.150591 35          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/29-14:02:24.150594 35 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/29-14:02:24.150594 35 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/29-14:02:24.150595 35 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/29-14:02:24.150595 35 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/29-14:02:24.150596 35 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/29-14:02:24.150596 35 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/29-14:02:24.150597 35 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/29-14:02:24.150597 35       Options.max_sequential_skip_in_iterations: 8
2025/07/29-14:02:24.150598 35                    Options.max_compaction_bytes: 1677721600
2025/07/29-14:02:24.150598 35                        Options.arena_block_size: 1048576
2025/07/29-14:02:24.150599 35   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/29-14:02:24.150599 35   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/29-14:02:24.150600 35       Options.rate_limit_delay_max_milliseconds: 100
2025/07/29-14:02:24.150600 35                Options.disable_auto_compactions: 0
2025/07/29-14:02:24.150604 35                        Options.compaction_style: kCompactionStyleLevel
2025/07/29-14:02:24.150605 35                          Options.compaction_pri: kMinOverlappingRatio
2025/07/29-14:02:24.150605 35 Options.compaction_options_universal.size_ratio: 1
2025/07/29-14:02:24.150606 35 Options.compaction_options_universal.min_merge_width: 2
2025/07/29-14:02:24.150606 35 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/29-14:02:24.150607 35 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/29-14:02:24.150607 35 Options.compaction_options_universal.compression_size_percent: -1
2025/07/29-14:02:24.150608 35 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/29-14:02:24.150608 35 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/29-14:02:24.150609 35 Options.compaction_options_fifo.allow_compaction: 0
2025/07/29-14:02:24.150616 35                   Options.table_properties_collectors: 
2025/07/29-14:02:24.150616 35                   Options.inplace_update_support: 0
2025/07/29-14:02:24.150617 35                 Options.inplace_update_num_locks: 10000
2025/07/29-14:02:24.150617 35               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/29-14:02:24.150618 35               Options.memtable_whole_key_filtering: 0
2025/07/29-14:02:24.150618 35   Options.memtable_huge_page_size: 0
2025/07/29-14:02:24.150619 35                           Options.bloom_locality: 0
2025/07/29-14:02:24.150619 35                    Options.max_successive_merges: 0
2025/07/29-14:02:24.150620 35                Options.optimize_filters_for_hits: 0
2025/07/29-14:02:24.150620 35                Options.paranoid_file_checks: 0
2025/07/29-14:02:24.150621 35                Options.force_consistency_checks: 1
2025/07/29-14:02:24.150621 35                Options.report_bg_io_stats: 0
2025/07/29-14:02:24.150621 35                               Options.ttl: 2592000
2025/07/29-14:02:24.150622 35          Options.periodic_compaction_seconds: 0
2025/07/29-14:02:24.150794 35                       Options.enable_blob_files: false
2025/07/29-14:02:24.150798 35                           Options.min_blob_size: 0
2025/07/29-14:02:24.150799 35                          Options.blob_file_size: 268435456
2025/07/29-14:02:24.150800 35                   Options.blob_compression_type: NoCompression
2025/07/29-14:02:24.150800 35          Options.enable_blob_garbage_collection: false
2025/07/29-14:02:24.150801 35      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/29-14:02:24.150803 35 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/29-14:02:24.150804 35          Options.blob_compaction_readahead_size: 0
2025/07/29-14:02:24.153486 35 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000004 succeeded,manifest_file_number is 4, next_file_number is 6, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/29-14:02:24.153494 35 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 0
2025/07/29-14:02:24.157259 35 [db/version_set.cc:4409] Creating manifest 8
2025/07/29-14:02:24.178011 35 EVENT_LOG_v1 {"time_micros": 1753797744177941, "job": 1, "event": "recovery_started", "wal_files": [5]}
2025/07/29-14:02:24.178030 35 [db/db_impl/db_impl_open.cc:888] Recovering log #5 mode 2
2025/07/29-14:02:24.190565 35 EVENT_LOG_v1 {"time_micros": 1753797744190511, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 9, "file_size": 1733, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 766, "index_size": 49, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 1402, "raw_average_key_size": 41, "raw_value_size": 187, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 34, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1753797744, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "576cbfed-01f0-4a77-87d5-8c456fe0bb71", "db_session_id": "ZG4TB7E5QFERH67TF43I", "orig_file_number": 9}}
2025/07/29-14:02:24.191499 35 [db/version_set.cc:4409] Creating manifest 10
2025/07/29-14:02:24.235727 35 EVENT_LOG_v1 {"time_micros": 1753797744235715, "job": 1, "event": "recovery_finished"}
2025/07/29-14:02:24.265234 35 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000005.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/29-14:02:24.265516 35 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7d3456390000
2025/07/29-14:02:24.267070 35 DB pointer 0x7d3456220000
2025/07/29-14:02:24.267704 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-14:02:24.267715 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.69 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.2      0.01              0.00         1    0.011       0      0       0.0       0.0
 Sum      1/0    1.69 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.2      0.01              0.00         1    0.011       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.2      0.01              0.00         1    0.011       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.2      0.01              0.00         1    0.011       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7d345ec60010#8 capacity: 942.37 MB collections: 1 last_copies: 0 last_secs: 5.6e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
