2025/07/29-13:52:36.671065 46 RocksDB version: 6.29.5
2025/07/29-13:52:36.671400 46 Git sha 0
2025/07/29-13:52:36.671403 46 Compile date 2024-11-15 11:22:58
2025/07/29-13:52:36.671405 46 DB SUMMARY
2025/07/29-13:52:36.671406 46 DB Session ID:  JB94WLAEXWJD2OQZDUAB
2025/07/29-13:52:36.672549 46 SST files in /var/lib/milvus/rdb_data dir, Total Num: 0, files: 
2025/07/29-13:52:36.672552 46 Write Ahead Log file in /var/lib/milvus/rdb_data: 
2025/07/29-13:52:36.672554 46                         Options.error_if_exists: 0
2025/07/29-13:52:36.672555 46                       Options.create_if_missing: 1
2025/07/29-13:52:36.672556 46                         Options.paranoid_checks: 1
2025/07/29-13:52:36.672556 46             Options.flush_verify_memtable_count: 1
2025/07/29-13:52:36.672557 46                               Options.track_and_verify_wals_in_manifest: 0
2025/07/29-13:52:36.672558 46                                     Options.env: 0x7ea4129aad00
2025/07/29-13:52:36.672559 46                                      Options.fs: PosixFileSystem
2025/07/29-13:52:36.672559 46                                Options.info_log: 0x7ea334c90140
2025/07/29-13:52:36.672560 46                Options.max_file_opening_threads: 16
2025/07/29-13:52:36.672560 46                              Options.statistics: (nil)
2025/07/29-13:52:36.672561 46                               Options.use_fsync: 0
2025/07/29-13:52:36.672562 46                       Options.max_log_file_size: 0
2025/07/29-13:52:36.672562 46                  Options.max_manifest_file_size: 1073741824
2025/07/29-13:52:36.672563 46                   Options.log_file_time_to_roll: 0
2025/07/29-13:52:36.672563 46                       Options.keep_log_file_num: 1000
2025/07/29-13:52:36.672564 46                    Options.recycle_log_file_num: 0
2025/07/29-13:52:36.672564 46                         Options.allow_fallocate: 1
2025/07/29-13:52:36.672565 46                        Options.allow_mmap_reads: 0
2025/07/29-13:52:36.672565 46                       Options.allow_mmap_writes: 0
2025/07/29-13:52:36.672566 46                        Options.use_direct_reads: 0
2025/07/29-13:52:36.672566 46                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/29-13:52:36.672566 46          Options.create_missing_column_families: 1
2025/07/29-13:52:36.672567 46                              Options.db_log_dir: 
2025/07/29-13:52:36.672567 46                                 Options.wal_dir: 
2025/07/29-13:52:36.672568 46                Options.table_cache_numshardbits: 6
2025/07/29-13:52:36.672568 46                         Options.WAL_ttl_seconds: 0
2025/07/29-13:52:36.672569 46                       Options.WAL_size_limit_MB: 0
2025/07/29-13:52:36.672569 46                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/29-13:52:36.672570 46             Options.manifest_preallocation_size: 4194304
2025/07/29-13:52:36.672570 46                     Options.is_fd_close_on_exec: 1
2025/07/29-13:52:36.672571 46                   Options.advise_random_on_open: 1
2025/07/29-13:52:36.672571 46                   Options.experimental_mempurge_threshold: 0.000000
2025/07/29-13:52:36.672575 46                    Options.db_write_buffer_size: 0
2025/07/29-13:52:36.672576 46                    Options.write_buffer_manager: 0x7ea337640280
2025/07/29-13:52:36.672576 46         Options.access_hint_on_compaction_start: 1
2025/07/29-13:52:36.672577 46  Options.new_table_reader_for_compaction_inputs: 0
2025/07/29-13:52:36.672577 46           Options.random_access_max_buffer_size: 1048576
2025/07/29-13:52:36.672577 46                      Options.use_adaptive_mutex: 0
2025/07/29-13:52:36.672578 46                            Options.rate_limiter: (nil)
2025/07/29-13:52:36.672579 46     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/29-13:52:36.672579 46                       Options.wal_recovery_mode: 2
2025/07/29-13:52:36.672580 46                  Options.enable_thread_tracking: 0
2025/07/29-13:52:36.672580 46                  Options.enable_pipelined_write: 0
2025/07/29-13:52:36.672581 46                  Options.unordered_write: 0
2025/07/29-13:52:36.674327 46         Options.allow_concurrent_memtable_write: 1
2025/07/29-13:52:36.674332 46      Options.enable_write_thread_adaptive_yield: 1
2025/07/29-13:52:36.674333 46             Options.write_thread_max_yield_usec: 100
2025/07/29-13:52:36.674334 46            Options.write_thread_slow_yield_usec: 3
2025/07/29-13:52:36.674334 46                               Options.row_cache: None
2025/07/29-13:52:36.674335 46                              Options.wal_filter: None
2025/07/29-13:52:36.674335 46             Options.avoid_flush_during_recovery: 0
2025/07/29-13:52:36.674336 46             Options.allow_ingest_behind: 0
2025/07/29-13:52:36.674336 46             Options.preserve_deletes: 0
2025/07/29-13:52:36.674337 46             Options.two_write_queues: 0
2025/07/29-13:52:36.674337 46             Options.manual_wal_flush: 0
2025/07/29-13:52:36.674338 46             Options.atomic_flush: 0
2025/07/29-13:52:36.674338 46             Options.avoid_unnecessary_blocking_io: 0
2025/07/29-13:52:36.674339 46                 Options.persist_stats_to_disk: 0
2025/07/29-13:52:36.674339 46                 Options.write_dbid_to_manifest: 0
2025/07/29-13:52:36.674340 46                 Options.log_readahead_size: 0
2025/07/29-13:52:36.674340 46                 Options.file_checksum_gen_factory: Unknown
2025/07/29-13:52:36.674341 46                 Options.best_efforts_recovery: 0
2025/07/29-13:52:36.674342 46                Options.max_bgerror_resume_count: 2147483647
2025/07/29-13:52:36.674342 46            Options.bgerror_resume_retry_interval: 1000000
2025/07/29-13:52:36.674343 46             Options.allow_data_in_errors: 0
2025/07/29-13:52:36.674343 46             Options.db_host_id: __hostname__
2025/07/29-13:52:36.674344 46             Options.max_background_jobs: 2
2025/07/29-13:52:36.674344 46             Options.max_background_compactions: -1
2025/07/29-13:52:36.674345 46             Options.max_subcompactions: 1
2025/07/29-13:52:36.674345 46             Options.avoid_flush_during_shutdown: 0
2025/07/29-13:52:36.674346 46           Options.writable_file_max_buffer_size: 1048576
2025/07/29-13:52:36.674346 46             Options.delayed_write_rate : 16777216
2025/07/29-13:52:36.674347 46             Options.max_total_wal_size: 0
2025/07/29-13:52:36.674347 46             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/29-13:52:36.674348 46                   Options.stats_dump_period_sec: 600
2025/07/29-13:52:36.674348 46                 Options.stats_persist_period_sec: 600
2025/07/29-13:52:36.674349 46                 Options.stats_history_buffer_size: 1048576
2025/07/29-13:52:36.674349 46                          Options.max_open_files: -1
2025/07/29-13:52:36.674350 46                          Options.bytes_per_sync: 0
2025/07/29-13:52:36.674350 46                      Options.wal_bytes_per_sync: 0
2025/07/29-13:52:36.674351 46                   Options.strict_bytes_per_sync: 0
2025/07/29-13:52:36.674351 46       Options.compaction_readahead_size: 0
2025/07/29-13:52:36.674352 46                  Options.max_background_flushes: 1
2025/07/29-13:52:36.674352 46 Compression algorithms supported:
2025/07/29-13:52:36.674355 46 	kZSTD supported: 1
2025/07/29-13:52:36.674356 46 	kXpressCompression supported: 0
2025/07/29-13:52:36.674357 46 	kBZip2Compression supported: 0
2025/07/29-13:52:36.674357 46 	kZSTDNotFinalCompression supported: 1
2025/07/29-13:52:36.674358 46 	kLZ4Compression supported: 0
2025/07/29-13:52:36.674359 46 	kZlibCompression supported: 0
2025/07/29-13:52:36.674359 46 	kLZ4HCCompression supported: 0
2025/07/29-13:52:36.674360 46 	kSnappyCompression supported: 0
2025/07/29-13:52:36.674363 46 Fast CRC32 supported: Not supported on x86
2025/07/29-13:52:36.699560 46 [db/db_impl/db_impl_open.cc:307] Creating manifest 1 
2025/07/29-13:52:36.740116 46 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000001
2025/07/29-13:52:36.742249 46 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/07/29-13:52:36.742272 46               Options.comparator: leveldb.BytewiseComparator
2025/07/29-13:52:36.742592 46           Options.merge_operator: None
2025/07/29-13:52:36.742597 46        Options.compaction_filter: None
2025/07/29-13:52:36.742598 46        Options.compaction_filter_factory: None
2025/07/29-13:52:36.742598 46  Options.sst_partitioner_factory: None
2025/07/29-13:52:36.742600 46         Options.memtable_factory: SkipListFactory
2025/07/29-13:52:36.742600 46            Options.table_factory: BlockBasedTable
2025/07/29-13:52:36.742630 46            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7ea337701280)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7ea337640010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 988147875
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/29-13:52:36.742631 46        Options.write_buffer_size: 67108864
2025/07/29-13:52:36.742632 46  Options.max_write_buffer_number: 2
2025/07/29-13:52:36.742633 46        Options.compression[0]: NoCompression
2025/07/29-13:52:36.742634 46        Options.compression[1]: NoCompression
2025/07/29-13:52:36.742634 46        Options.compression[2]: ZSTD
2025/07/29-13:52:36.742635 46        Options.compression[3]: ZSTD
2025/07/29-13:52:36.742635 46        Options.compression[4]: ZSTD
2025/07/29-13:52:36.742636 46                  Options.bottommost_compression: Disabled
2025/07/29-13:52:36.742636 46       Options.prefix_extractor: nullptr
2025/07/29-13:52:36.742637 46   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/29-13:52:36.742637 46             Options.num_levels: 5
2025/07/29-13:52:36.742637 46        Options.min_write_buffer_number_to_merge: 1
2025/07/29-13:52:36.742638 46     Options.max_write_buffer_number_to_maintain: 0
2025/07/29-13:52:36.742638 46     Options.max_write_buffer_size_to_maintain: 0
2025/07/29-13:52:36.742639 46            Options.bottommost_compression_opts.window_bits: -14
2025/07/29-13:52:36.742639 46                  Options.bottommost_compression_opts.level: 32767
2025/07/29-13:52:36.742640 46               Options.bottommost_compression_opts.strategy: 0
2025/07/29-13:52:36.742640 46         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/29-13:52:36.742641 46         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/29-13:52:36.742641 46         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/29-13:52:36.742641 46                  Options.bottommost_compression_opts.enabled: false
2025/07/29-13:52:36.742642 46         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/29-13:52:36.742643 46            Options.compression_opts.window_bits: -14
2025/07/29-13:52:36.742643 46                  Options.compression_opts.level: 32767
2025/07/29-13:52:36.742644 46               Options.compression_opts.strategy: 0
2025/07/29-13:52:36.742644 46         Options.compression_opts.max_dict_bytes: 0
2025/07/29-13:52:36.742644 46         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/29-13:52:36.742645 46         Options.compression_opts.parallel_threads: 1
2025/07/29-13:52:36.742645 46                  Options.compression_opts.enabled: false
2025/07/29-13:52:36.742646 46         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/29-13:52:36.742997 46      Options.level0_file_num_compaction_trigger: 4
2025/07/29-13:52:36.742999 46          Options.level0_slowdown_writes_trigger: 20
2025/07/29-13:52:36.742999 46              Options.level0_stop_writes_trigger: 36
2025/07/29-13:52:36.743000 46                   Options.target_file_size_base: 67108864
2025/07/29-13:52:36.743001 46             Options.target_file_size_multiplier: 2
2025/07/29-13:52:36.743001 46                Options.max_bytes_for_level_base: 268435456
2025/07/29-13:52:36.743002 46 Options.level_compaction_dynamic_level_bytes: 0
2025/07/29-13:52:36.743002 46          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/29-13:52:36.743005 46 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/29-13:52:36.743006 46 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/29-13:52:36.743006 46 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/29-13:52:36.743007 46 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/29-13:52:36.743007 46 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/29-13:52:36.743008 46 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/29-13:52:36.743008 46 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/29-13:52:36.743009 46       Options.max_sequential_skip_in_iterations: 8
2025/07/29-13:52:36.743009 46                    Options.max_compaction_bytes: 1677721600
2025/07/29-13:52:36.743010 46                        Options.arena_block_size: 1048576
2025/07/29-13:52:36.743010 46   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/29-13:52:36.743011 46   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/29-13:52:36.743011 46       Options.rate_limit_delay_max_milliseconds: 100
2025/07/29-13:52:36.743012 46                Options.disable_auto_compactions: 0
2025/07/29-13:52:36.743017 46                        Options.compaction_style: kCompactionStyleLevel
2025/07/29-13:52:36.743017 46                          Options.compaction_pri: kMinOverlappingRatio
2025/07/29-13:52:36.743018 46 Options.compaction_options_universal.size_ratio: 1
2025/07/29-13:52:36.743018 46 Options.compaction_options_universal.min_merge_width: 2
2025/07/29-13:52:36.743019 46 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/29-13:52:36.743019 46 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/29-13:52:36.743020 46 Options.compaction_options_universal.compression_size_percent: -1
2025/07/29-13:52:36.743021 46 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/29-13:52:36.743021 46 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/29-13:52:36.743022 46 Options.compaction_options_fifo.allow_compaction: 0
2025/07/29-13:52:36.743030 46                   Options.table_properties_collectors: 
2025/07/29-13:52:36.743030 46                   Options.inplace_update_support: 0
2025/07/29-13:52:36.743031 46                 Options.inplace_update_num_locks: 10000
2025/07/29-13:52:36.743031 46               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/29-13:52:36.743032 46               Options.memtable_whole_key_filtering: 0
2025/07/29-13:52:36.743033 46   Options.memtable_huge_page_size: 0
2025/07/29-13:52:36.743033 46                           Options.bloom_locality: 0
2025/07/29-13:52:36.743033 46                    Options.max_successive_merges: 0
2025/07/29-13:52:36.743034 46                Options.optimize_filters_for_hits: 0
2025/07/29-13:52:36.743034 46                Options.paranoid_file_checks: 0
2025/07/29-13:52:36.743035 46                Options.force_consistency_checks: 1
2025/07/29-13:52:36.743035 46                Options.report_bg_io_stats: 0
2025/07/29-13:52:36.743036 46                               Options.ttl: 2592000
2025/07/29-13:52:36.743036 46          Options.periodic_compaction_seconds: 0
2025/07/29-13:52:36.743036 46                       Options.enable_blob_files: false
2025/07/29-13:52:36.743037 46                           Options.min_blob_size: 0
2025/07/29-13:52:36.743235 46                          Options.blob_file_size: 268435456
2025/07/29-13:52:36.743240 46                   Options.blob_compression_type: NoCompression
2025/07/29-13:52:36.743241 46          Options.enable_blob_garbage_collection: false
2025/07/29-13:52:36.743241 46      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/29-13:52:36.743244 46 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/29-13:52:36.743244 46          Options.blob_compaction_readahead_size: 0
2025/07/29-13:52:36.746519 46 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/29-13:52:36.746529 46 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 0
2025/07/29-13:52:36.752193 46 [db/version_set.cc:4409] Creating manifest 4
2025/07/29-13:52:36.787184 46 [db/column_family.cc:605] --------------- Options for column family [properties]:
2025/07/29-13:52:36.787196 46               Options.comparator: leveldb.BytewiseComparator
2025/07/29-13:52:36.787197 46           Options.merge_operator: None
2025/07/29-13:52:36.787198 46        Options.compaction_filter: None
2025/07/29-13:52:36.787199 46        Options.compaction_filter_factory: None
2025/07/29-13:52:36.787199 46  Options.sst_partitioner_factory: None
2025/07/29-13:52:36.787200 46         Options.memtable_factory: SkipListFactory
2025/07/29-13:52:36.787200 46            Options.table_factory: BlockBasedTable
2025/07/29-13:52:36.787214 46            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7ea337701280)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7ea337640010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 988147875
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/29-13:52:36.787215 46        Options.write_buffer_size: 67108864
2025/07/29-13:52:36.787216 46  Options.max_write_buffer_number: 2
2025/07/29-13:52:36.787216 46        Options.compression[0]: NoCompression
2025/07/29-13:52:36.787217 46        Options.compression[1]: NoCompression
2025/07/29-13:52:36.787218 46        Options.compression[2]: ZSTD
2025/07/29-13:52:36.787218 46        Options.compression[3]: ZSTD
2025/07/29-13:52:36.787219 46        Options.compression[4]: ZSTD
2025/07/29-13:52:36.787219 46                  Options.bottommost_compression: Disabled
2025/07/29-13:52:36.787220 46       Options.prefix_extractor: nullptr
2025/07/29-13:52:36.787220 46   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/29-13:52:36.787221 46             Options.num_levels: 5
2025/07/29-13:52:36.787221 46        Options.min_write_buffer_number_to_merge: 1
2025/07/29-13:52:36.787221 46     Options.max_write_buffer_number_to_maintain: 0
2025/07/29-13:52:36.787222 46     Options.max_write_buffer_size_to_maintain: 0
2025/07/29-13:52:36.787222 46            Options.bottommost_compression_opts.window_bits: -14
2025/07/29-13:52:36.787223 46                  Options.bottommost_compression_opts.level: 32767
2025/07/29-13:52:36.787223 46               Options.bottommost_compression_opts.strategy: 0
2025/07/29-13:52:36.787224 46         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/29-13:52:36.787224 46         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/29-13:52:36.787225 46         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/29-13:52:36.787226 46                  Options.bottommost_compression_opts.enabled: false
2025/07/29-13:52:36.787226 46         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/29-13:52:36.787227 46            Options.compression_opts.window_bits: -14
2025/07/29-13:52:36.787227 46                  Options.compression_opts.level: 32767
2025/07/29-13:52:36.787228 46               Options.compression_opts.strategy: 0
2025/07/29-13:52:36.787228 46         Options.compression_opts.max_dict_bytes: 0
2025/07/29-13:52:36.787229 46         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/29-13:52:36.787229 46         Options.compression_opts.parallel_threads: 1
2025/07/29-13:52:36.787364 46                  Options.compression_opts.enabled: false
2025/07/29-13:52:36.787369 46         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/29-13:52:36.787369 46      Options.level0_file_num_compaction_trigger: 4
2025/07/29-13:52:36.787370 46          Options.level0_slowdown_writes_trigger: 20
2025/07/29-13:52:36.787371 46              Options.level0_stop_writes_trigger: 36
2025/07/29-13:52:36.787371 46                   Options.target_file_size_base: 67108864
2025/07/29-13:52:36.787372 46             Options.target_file_size_multiplier: 2
2025/07/29-13:52:36.787372 46                Options.max_bytes_for_level_base: 268435456
2025/07/29-13:52:36.787373 46 Options.level_compaction_dynamic_level_bytes: 0
2025/07/29-13:52:36.787373 46          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/29-13:52:36.787376 46 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/29-13:52:36.787376 46 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/29-13:52:36.787377 46 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/29-13:52:36.787377 46 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/29-13:52:36.787378 46 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/29-13:52:36.787378 46 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/29-13:52:36.787379 46 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/29-13:52:36.787379 46       Options.max_sequential_skip_in_iterations: 8
2025/07/29-13:52:36.787380 46                    Options.max_compaction_bytes: 1677721600
2025/07/29-13:52:36.787380 46                        Options.arena_block_size: 1048576
2025/07/29-13:52:36.787381 46   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/29-13:52:36.787381 46   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/29-13:52:36.787382 46       Options.rate_limit_delay_max_milliseconds: 100
2025/07/29-13:52:36.787382 46                Options.disable_auto_compactions: 0
2025/07/29-13:52:36.787386 46                        Options.compaction_style: kCompactionStyleLevel
2025/07/29-13:52:36.787387 46                          Options.compaction_pri: kMinOverlappingRatio
2025/07/29-13:52:36.787388 46 Options.compaction_options_universal.size_ratio: 1
2025/07/29-13:52:36.787388 46 Options.compaction_options_universal.min_merge_width: 2
2025/07/29-13:52:36.787389 46 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/29-13:52:36.787389 46 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/29-13:52:36.787390 46 Options.compaction_options_universal.compression_size_percent: -1
2025/07/29-13:52:36.787391 46 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/29-13:52:36.787391 46 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/29-13:52:36.787392 46 Options.compaction_options_fifo.allow_compaction: 0
2025/07/29-13:52:36.787398 46                   Options.table_properties_collectors: 
2025/07/29-13:52:36.787399 46                   Options.inplace_update_support: 0
2025/07/29-13:52:36.787399 46                 Options.inplace_update_num_locks: 10000
2025/07/29-13:52:36.787400 46               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/29-13:52:36.787401 46               Options.memtable_whole_key_filtering: 0
2025/07/29-13:52:36.787401 46   Options.memtable_huge_page_size: 0
2025/07/29-13:52:36.787402 46                           Options.bloom_locality: 0
2025/07/29-13:52:36.787402 46                    Options.max_successive_merges: 0
2025/07/29-13:52:36.787403 46                Options.optimize_filters_for_hits: 0
2025/07/29-13:52:36.787403 46                Options.paranoid_file_checks: 0
2025/07/29-13:52:36.787403 46                Options.force_consistency_checks: 1
2025/07/29-13:52:36.787404 46                Options.report_bg_io_stats: 0
2025/07/29-13:52:36.787404 46                               Options.ttl: 2592000
2025/07/29-13:52:36.787405 46          Options.periodic_compaction_seconds: 0
2025/07/29-13:52:36.787550 46                       Options.enable_blob_files: false
2025/07/29-13:52:36.787553 46                           Options.min_blob_size: 0
2025/07/29-13:52:36.787553 46                          Options.blob_file_size: 268435456
2025/07/29-13:52:36.787554 46                   Options.blob_compression_type: NoCompression
2025/07/29-13:52:36.787554 46          Options.enable_blob_garbage_collection: false
2025/07/29-13:52:36.787555 46      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/29-13:52:36.787556 46 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/29-13:52:36.787557 46          Options.blob_compaction_readahead_size: 0
2025/07/29-13:52:36.788394 46 [db/db_impl/db_impl.cc:2780] Created column family [properties] (ID 1)
2025/07/29-13:52:36.829801 46 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7ea334d80700
2025/07/29-13:52:36.831664 46 DB pointer 0x7ea334c21c00
2025/07/29-13:52:39.832150 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-13:52:39.832168 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.1 total, 3.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.1 total, 3.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7ea337640010#8 capacity: 942.37 MB collections: 1 last_copies: 2 last_secs: 7.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7ea337640010#8 capacity: 942.37 MB collections: 1 last_copies: 2 last_secs: 7.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
