/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by MockGen. DO NOT EDIT.
// Source: knowledge.go
//
// Generated by this command:
//
//	mockgen -destination knowledgemock/knowledge_mock.go --package knowledgemock -source knowledge.go
//

// Package knowledgemock is a generated GoMock package.
package knowledgemock

import (
	context "context"
	reflect "reflect"

	knowledge "github.com/coze-dev/coze-studio/backend/domain/workflow/crossdomain/knowledge"
	gomock "go.uber.org/mock/gomock"
)

// MockKnowledgeOperator is a mock of KnowledgeOperator interface.
type MockKnowledgeOperator struct {
	ctrl     *gomock.Controller
	recorder *MockKnowledgeOperatorMockRecorder
	isgomock struct{}
}

// MockKnowledgeOperatorMockRecorder is the mock recorder for MockKnowledgeOperator.
type MockKnowledgeOperatorMockRecorder struct {
	mock *MockKnowledgeOperator
}

// NewMockKnowledgeOperator creates a new mock instance.
func NewMockKnowledgeOperator(ctrl *gomock.Controller) *MockKnowledgeOperator {
	mock := &MockKnowledgeOperator{ctrl: ctrl}
	mock.recorder = &MockKnowledgeOperatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKnowledgeOperator) EXPECT() *MockKnowledgeOperatorMockRecorder {
	return m.recorder
}

// Delete mocks base method.
func (m *MockKnowledgeOperator) Delete(arg0 context.Context, arg1 *knowledge.DeleteDocumentRequest) (*knowledge.DeleteDocumentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", arg0, arg1)
	ret0, _ := ret[0].(*knowledge.DeleteDocumentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Delete indicates an expected call of Delete.
func (mr *MockKnowledgeOperatorMockRecorder) Delete(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockKnowledgeOperator)(nil).Delete), arg0, arg1)
}

// ListKnowledgeDetail mocks base method.
func (m *MockKnowledgeOperator) ListKnowledgeDetail(arg0 context.Context, arg1 *knowledge.ListKnowledgeDetailRequest) (*knowledge.ListKnowledgeDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListKnowledgeDetail", arg0, arg1)
	ret0, _ := ret[0].(*knowledge.ListKnowledgeDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListKnowledgeDetail indicates an expected call of ListKnowledgeDetail.
func (mr *MockKnowledgeOperatorMockRecorder) ListKnowledgeDetail(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListKnowledgeDetail", reflect.TypeOf((*MockKnowledgeOperator)(nil).ListKnowledgeDetail), arg0, arg1)
}

// Retrieve mocks base method.
func (m *MockKnowledgeOperator) Retrieve(arg0 context.Context, arg1 *knowledge.RetrieveRequest) (*knowledge.RetrieveResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Retrieve", arg0, arg1)
	ret0, _ := ret[0].(*knowledge.RetrieveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Retrieve indicates an expected call of Retrieve.
func (mr *MockKnowledgeOperatorMockRecorder) Retrieve(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Retrieve", reflect.TypeOf((*MockKnowledgeOperator)(nil).Retrieve), arg0, arg1)
}

// Store mocks base method.
func (m *MockKnowledgeOperator) Store(ctx context.Context, document *knowledge.CreateDocumentRequest) (*knowledge.CreateDocumentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Store", ctx, document)
	ret0, _ := ret[0].(*knowledge.CreateDocumentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Store indicates an expected call of Store.
func (mr *MockKnowledgeOperatorMockRecorder) Store(ctx, document any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Store", reflect.TypeOf((*MockKnowledgeOperator)(nil).Store), ctx, document)
}
