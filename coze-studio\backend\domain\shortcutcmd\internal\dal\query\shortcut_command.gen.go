// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/shortcutcmd/internal/dal/model"
)

func newShortcutCommand(db *gorm.DB, opts ...gen.DOOption) shortcutCommand {
	_shortcutCommand := shortcutCommand{}

	_shortcutCommand.shortcutCommandDo.UseDB(db, opts...)
	_shortcutCommand.shortcutCommandDo.UseModel(&model.ShortcutCommand{})

	tableName := _shortcutCommand.shortcutCommandDo.TableName()
	_shortcutCommand.ALL = field.NewAsterisk(tableName)
	_shortcutCommand.ID = field.NewInt64(tableName, "id")
	_shortcutCommand.ObjectID = field.NewInt64(tableName, "object_id")
	_shortcutCommand.CommandID = field.NewInt64(tableName, "command_id")
	_shortcutCommand.CommandName = field.NewString(tableName, "command_name")
	_shortcutCommand.ShortcutCommand = field.NewString(tableName, "shortcut_command")
	_shortcutCommand.Description = field.NewString(tableName, "description")
	_shortcutCommand.SendType = field.NewInt32(tableName, "send_type")
	_shortcutCommand.ToolType = field.NewInt32(tableName, "tool_type")
	_shortcutCommand.WorkFlowID = field.NewInt64(tableName, "work_flow_id")
	_shortcutCommand.PluginID = field.NewInt64(tableName, "plugin_id")
	_shortcutCommand.PluginToolName = field.NewString(tableName, "plugin_tool_name")
	_shortcutCommand.TemplateQuery = field.NewString(tableName, "template_query")
	_shortcutCommand.Components = field.NewField(tableName, "components")
	_shortcutCommand.CardSchema = field.NewString(tableName, "card_schema")
	_shortcutCommand.ToolInfo = field.NewField(tableName, "tool_info")
	_shortcutCommand.Status = field.NewInt32(tableName, "status")
	_shortcutCommand.CreatorID = field.NewInt64(tableName, "creator_id")
	_shortcutCommand.IsOnline = field.NewInt32(tableName, "is_online")
	_shortcutCommand.CreatedAt = field.NewInt64(tableName, "created_at")
	_shortcutCommand.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_shortcutCommand.AgentID = field.NewInt64(tableName, "agent_id")
	_shortcutCommand.ShortcutIcon = field.NewField(tableName, "shortcut_icon")
	_shortcutCommand.PluginToolID = field.NewInt64(tableName, "plugin_tool_id")

	_shortcutCommand.fillFieldMap()

	return _shortcutCommand
}

// shortcutCommand bot快捷指令表
type shortcutCommand struct {
	shortcutCommandDo

	ALL             field.Asterisk
	ID              field.Int64  // 主键ID
	ObjectID        field.Int64  // 实体ID,该实体可用这个指令
	CommandID       field.Int64  // 命令ID
	CommandName     field.String // 命令名称
	ShortcutCommand field.String // 快捷指令
	Description     field.String // 命令描述
	SendType        field.Int32  // 发送类型 0:query 1:panel
	ToolType        field.Int32  // 使用工具的type 1:workFlow 2:插件
	WorkFlowID      field.Int64  // 使用workFlow的id
	PluginID        field.Int64  // 使用插件的id
	PluginToolName  field.String // 使用插件的api_name
	TemplateQuery   field.String // query模板
	Components      field.Field  // panel参数
	CardSchema      field.String // 卡片schema
	ToolInfo        field.Field  // 工具信息 包含name+变量列表
	Status          field.Int32  // 状态,0无效,1有效
	CreatorID       field.Int64  // 创建者ID
	IsOnline        field.Int32  // 是否为线上信息 0草稿 1线上
	CreatedAt       field.Int64  // 创建时间
	UpdatedAt       field.Int64  // 更新时间
	AgentID         field.Int64  // multi的指令时，该指令由哪个节点执行
	ShortcutIcon    field.Field  // 快捷指令图标
	PluginToolID    field.Int64  // tool_id

	fieldMap map[string]field.Expr
}

func (s shortcutCommand) Table(newTableName string) *shortcutCommand {
	s.shortcutCommandDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s shortcutCommand) As(alias string) *shortcutCommand {
	s.shortcutCommandDo.DO = *(s.shortcutCommandDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *shortcutCommand) updateTableName(table string) *shortcutCommand {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ObjectID = field.NewInt64(table, "object_id")
	s.CommandID = field.NewInt64(table, "command_id")
	s.CommandName = field.NewString(table, "command_name")
	s.ShortcutCommand = field.NewString(table, "shortcut_command")
	s.Description = field.NewString(table, "description")
	s.SendType = field.NewInt32(table, "send_type")
	s.ToolType = field.NewInt32(table, "tool_type")
	s.WorkFlowID = field.NewInt64(table, "work_flow_id")
	s.PluginID = field.NewInt64(table, "plugin_id")
	s.PluginToolName = field.NewString(table, "plugin_tool_name")
	s.TemplateQuery = field.NewString(table, "template_query")
	s.Components = field.NewField(table, "components")
	s.CardSchema = field.NewString(table, "card_schema")
	s.ToolInfo = field.NewField(table, "tool_info")
	s.Status = field.NewInt32(table, "status")
	s.CreatorID = field.NewInt64(table, "creator_id")
	s.IsOnline = field.NewInt32(table, "is_online")
	s.CreatedAt = field.NewInt64(table, "created_at")
	s.UpdatedAt = field.NewInt64(table, "updated_at")
	s.AgentID = field.NewInt64(table, "agent_id")
	s.ShortcutIcon = field.NewField(table, "shortcut_icon")
	s.PluginToolID = field.NewInt64(table, "plugin_tool_id")

	s.fillFieldMap()

	return s
}

func (s *shortcutCommand) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *shortcutCommand) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 23)
	s.fieldMap["id"] = s.ID
	s.fieldMap["object_id"] = s.ObjectID
	s.fieldMap["command_id"] = s.CommandID
	s.fieldMap["command_name"] = s.CommandName
	s.fieldMap["shortcut_command"] = s.ShortcutCommand
	s.fieldMap["description"] = s.Description
	s.fieldMap["send_type"] = s.SendType
	s.fieldMap["tool_type"] = s.ToolType
	s.fieldMap["work_flow_id"] = s.WorkFlowID
	s.fieldMap["plugin_id"] = s.PluginID
	s.fieldMap["plugin_tool_name"] = s.PluginToolName
	s.fieldMap["template_query"] = s.TemplateQuery
	s.fieldMap["components"] = s.Components
	s.fieldMap["card_schema"] = s.CardSchema
	s.fieldMap["tool_info"] = s.ToolInfo
	s.fieldMap["status"] = s.Status
	s.fieldMap["creator_id"] = s.CreatorID
	s.fieldMap["is_online"] = s.IsOnline
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["agent_id"] = s.AgentID
	s.fieldMap["shortcut_icon"] = s.ShortcutIcon
	s.fieldMap["plugin_tool_id"] = s.PluginToolID
}

func (s shortcutCommand) clone(db *gorm.DB) shortcutCommand {
	s.shortcutCommandDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s shortcutCommand) replaceDB(db *gorm.DB) shortcutCommand {
	s.shortcutCommandDo.ReplaceDB(db)
	return s
}

type shortcutCommandDo struct{ gen.DO }

type IShortcutCommandDo interface {
	gen.SubQuery
	Debug() IShortcutCommandDo
	WithContext(ctx context.Context) IShortcutCommandDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IShortcutCommandDo
	WriteDB() IShortcutCommandDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IShortcutCommandDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IShortcutCommandDo
	Not(conds ...gen.Condition) IShortcutCommandDo
	Or(conds ...gen.Condition) IShortcutCommandDo
	Select(conds ...field.Expr) IShortcutCommandDo
	Where(conds ...gen.Condition) IShortcutCommandDo
	Order(conds ...field.Expr) IShortcutCommandDo
	Distinct(cols ...field.Expr) IShortcutCommandDo
	Omit(cols ...field.Expr) IShortcutCommandDo
	Join(table schema.Tabler, on ...field.Expr) IShortcutCommandDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IShortcutCommandDo
	RightJoin(table schema.Tabler, on ...field.Expr) IShortcutCommandDo
	Group(cols ...field.Expr) IShortcutCommandDo
	Having(conds ...gen.Condition) IShortcutCommandDo
	Limit(limit int) IShortcutCommandDo
	Offset(offset int) IShortcutCommandDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IShortcutCommandDo
	Unscoped() IShortcutCommandDo
	Create(values ...*model.ShortcutCommand) error
	CreateInBatches(values []*model.ShortcutCommand, batchSize int) error
	Save(values ...*model.ShortcutCommand) error
	First() (*model.ShortcutCommand, error)
	Take() (*model.ShortcutCommand, error)
	Last() (*model.ShortcutCommand, error)
	Find() ([]*model.ShortcutCommand, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ShortcutCommand, err error)
	FindInBatches(result *[]*model.ShortcutCommand, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ShortcutCommand) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IShortcutCommandDo
	Assign(attrs ...field.AssignExpr) IShortcutCommandDo
	Joins(fields ...field.RelationField) IShortcutCommandDo
	Preload(fields ...field.RelationField) IShortcutCommandDo
	FirstOrInit() (*model.ShortcutCommand, error)
	FirstOrCreate() (*model.ShortcutCommand, error)
	FindByPage(offset int, limit int) (result []*model.ShortcutCommand, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IShortcutCommandDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s shortcutCommandDo) Debug() IShortcutCommandDo {
	return s.withDO(s.DO.Debug())
}

func (s shortcutCommandDo) WithContext(ctx context.Context) IShortcutCommandDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s shortcutCommandDo) ReadDB() IShortcutCommandDo {
	return s.Clauses(dbresolver.Read)
}

func (s shortcutCommandDo) WriteDB() IShortcutCommandDo {
	return s.Clauses(dbresolver.Write)
}

func (s shortcutCommandDo) Session(config *gorm.Session) IShortcutCommandDo {
	return s.withDO(s.DO.Session(config))
}

func (s shortcutCommandDo) Clauses(conds ...clause.Expression) IShortcutCommandDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s shortcutCommandDo) Returning(value interface{}, columns ...string) IShortcutCommandDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s shortcutCommandDo) Not(conds ...gen.Condition) IShortcutCommandDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s shortcutCommandDo) Or(conds ...gen.Condition) IShortcutCommandDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s shortcutCommandDo) Select(conds ...field.Expr) IShortcutCommandDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s shortcutCommandDo) Where(conds ...gen.Condition) IShortcutCommandDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s shortcutCommandDo) Order(conds ...field.Expr) IShortcutCommandDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s shortcutCommandDo) Distinct(cols ...field.Expr) IShortcutCommandDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s shortcutCommandDo) Omit(cols ...field.Expr) IShortcutCommandDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s shortcutCommandDo) Join(table schema.Tabler, on ...field.Expr) IShortcutCommandDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s shortcutCommandDo) LeftJoin(table schema.Tabler, on ...field.Expr) IShortcutCommandDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s shortcutCommandDo) RightJoin(table schema.Tabler, on ...field.Expr) IShortcutCommandDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s shortcutCommandDo) Group(cols ...field.Expr) IShortcutCommandDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s shortcutCommandDo) Having(conds ...gen.Condition) IShortcutCommandDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s shortcutCommandDo) Limit(limit int) IShortcutCommandDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s shortcutCommandDo) Offset(offset int) IShortcutCommandDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s shortcutCommandDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IShortcutCommandDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s shortcutCommandDo) Unscoped() IShortcutCommandDo {
	return s.withDO(s.DO.Unscoped())
}

func (s shortcutCommandDo) Create(values ...*model.ShortcutCommand) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s shortcutCommandDo) CreateInBatches(values []*model.ShortcutCommand, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s shortcutCommandDo) Save(values ...*model.ShortcutCommand) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s shortcutCommandDo) First() (*model.ShortcutCommand, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ShortcutCommand), nil
	}
}

func (s shortcutCommandDo) Take() (*model.ShortcutCommand, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ShortcutCommand), nil
	}
}

func (s shortcutCommandDo) Last() (*model.ShortcutCommand, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ShortcutCommand), nil
	}
}

func (s shortcutCommandDo) Find() ([]*model.ShortcutCommand, error) {
	result, err := s.DO.Find()
	return result.([]*model.ShortcutCommand), err
}

func (s shortcutCommandDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ShortcutCommand, err error) {
	buf := make([]*model.ShortcutCommand, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s shortcutCommandDo) FindInBatches(result *[]*model.ShortcutCommand, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s shortcutCommandDo) Attrs(attrs ...field.AssignExpr) IShortcutCommandDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s shortcutCommandDo) Assign(attrs ...field.AssignExpr) IShortcutCommandDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s shortcutCommandDo) Joins(fields ...field.RelationField) IShortcutCommandDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s shortcutCommandDo) Preload(fields ...field.RelationField) IShortcutCommandDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s shortcutCommandDo) FirstOrInit() (*model.ShortcutCommand, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ShortcutCommand), nil
	}
}

func (s shortcutCommandDo) FirstOrCreate() (*model.ShortcutCommand, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ShortcutCommand), nil
	}
}

func (s shortcutCommandDo) FindByPage(offset int, limit int) (result []*model.ShortcutCommand, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s shortcutCommandDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s shortcutCommandDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s shortcutCommandDo) Delete(models ...*model.ShortcutCommand) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *shortcutCommandDo) withDO(do gen.Dao) *shortcutCommandDo {
	s.DO = *do.(*gen.DO)
	return s
}
