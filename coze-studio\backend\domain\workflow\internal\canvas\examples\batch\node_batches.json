{"nodes": [{"blocks": [], "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"name": "input", "required": false, "schema": {"type": "string"}, "type": "list"}], "trigger_parameters": []}, "edges": null, "id": "100001", "meta": {"position": {"x": -90.5329099821747, "y": -323.84999999999985}}, "type": "1"}, {"blocks": [], "data": {"inputs": {"inputParameters": [{"input": {"schema": {"schema": [{"name": "output", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "input", "required": false, "type": "string"}], "type": "object"}, "type": "list", "value": {"content": {"blockID": "178876", "name": "outputList", "source": "block-output"}, "rawMeta": {"type": 103}, "type": "ref"}}, "name": "output"}], "terminatePlan": "returnVariables"}, "nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}}, "edges": null, "id": "900001", "meta": {"position": {"x": 1209.0861892268267, "y": -336.84999999999985}}, "type": "2"}, {"blocks": [], "data": {"inputs": {"batch": {"batchEnable": true, "batchSize": 100, "concurrentSize": 10, "inputLists": [{"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "100001", "name": "input", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "item1"}]}, "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "180671", "name": "item1", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "item1"}, {"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "100001", "name": "input", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "input_from_entry"}], "llmParam": [{"input": {"type": "integer", "value": {"content": "1737521813", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "豆包·1.5·Pro·32k", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "modleName"}, {"input": {"type": "string", "value": {"content": "balance", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "float", "value": {"content": "0.8", "rawMeta": {"type": 4}, "type": "literal"}}, "name": "temperature"}, {"input": {"type": "integer", "value": {"content": "4096", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "integer", "value": {"content": "0", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "string", "value": {"content": "{{item1}}\nall questions:\n{{input}}", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "rawMeta": {"type": 3}, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "systemPrompt"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 180000}}, "nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "mainColor": "#5C62FF", "subTitle": "大模型", "title": "大模型"}, "outputs": [{"name": "outputList", "schema": {"schema": [{"name": "output", "type": "string"}], "type": "object"}, "type": "list"}], "version": "3"}, "edges": null, "id": "180671", "meta": {"position": {"x": 342.6734564208258, "y": -363.54999999999984}}, "type": "3"}, {"blocks": [], "data": {"inputs": {"batch": {"batchEnable": true, "batchSize": 100, "concurrentSize": 10, "inputLists": [{"input": {"schema": {"schema": [{"name": "output", "type": "string"}], "type": "object"}, "type": "list", "value": {"content": {"blockID": "180671", "name": "outputList", "source": "block-output"}, "rawMeta": {"type": 103}, "type": "ref"}}, "name": "item1"}]}, "inputDefs": [{"input": {}, "name": "input", "required": false, "type": "string"}], "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "178876", "name": "item1.output", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "input"}], "settingOnError": {"dataOnErr": "{\n    \"outputList\": [\n        {\n            \"output\": []\n        }\n    ]\n}", "switch": false}, "spaceId": "7309328955423670309", "type": 0, "workflowId": "7469707607914217512", "workflowVersion": "v0.0.1"}, "nodeMeta": {"description": "文本处理", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Workflow-v2.jpg", "isImageflow": false, "title": "text_processing"}, "outputs": [{"name": "outputList", "schema": {"schema": [{"name": "output", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "input", "required": false, "type": "string"}], "type": "object"}, "type": "list"}]}, "edges": null, "id": "178876", "meta": {"position": {"x": 775.8798228238263, "y": -337.54999999999984}}, "type": "9"}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "180671", "sourcePortID": ""}, {"sourceNodeID": "178876", "targetNodeID": "900001", "sourcePortID": ""}, {"sourceNodeID": "180671", "targetNodeID": "178876", "sourcePortID": ""}], "versions": {"loop": "v2"}}