2025/07/29-14:07:27.489403 43 RocksDB version: 6.29.5
2025/07/29-14:07:27.489807 43 Git sha 0
2025/07/29-14:07:27.489811 43 Compile date 2024-11-15 11:22:58
2025/07/29-14:07:27.489814 43 DB SUMMARY
2025/07/29-14:07:27.489815 43 DB Session ID:  ITIJJB5VQ9YSIJ2X9MH9
2025/07/29-14:07:27.493185 43 CURRENT file:  CURRENT
2025/07/29-14:07:27.493209 43 IDENTITY file:  IDENTITY
2025/07/29-14:07:27.493814 43 MANIFEST file:  MANIFEST-000011 size: 147 Bytes
2025/07/29-14:07:27.493825 43 SST files in /var/lib/milvus/rdb_data dir, Total Num: 0, files: 
2025/07/29-14:07:27.493827 43 Write Ahead Log file in /var/lib/milvus/rdb_data: 000005.log size: 0 ; 000012.log size: 0 ; 
2025/07/29-14:07:27.493830 43                         Options.error_if_exists: 0
2025/07/29-14:07:27.493831 43                       Options.create_if_missing: 1
2025/07/29-14:07:27.493832 43                         Options.paranoid_checks: 1
2025/07/29-14:07:27.493832 43             Options.flush_verify_memtable_count: 1
2025/07/29-14:07:27.493833 43                               Options.track_and_verify_wals_in_manifest: 0
2025/07/29-14:07:27.493833 43                                     Options.env: 0x7a42ae10cd00
2025/07/29-14:07:27.493835 43                                      Options.fs: PosixFileSystem
2025/07/29-14:07:27.493836 43                                Options.info_log: 0x7a41d1090140
2025/07/29-14:07:27.493836 43                Options.max_file_opening_threads: 16
2025/07/29-14:07:27.493837 43                              Options.statistics: (nil)
2025/07/29-14:07:27.493838 43                               Options.use_fsync: 0
2025/07/29-14:07:27.493838 43                       Options.max_log_file_size: 0
2025/07/29-14:07:27.493839 43                  Options.max_manifest_file_size: 1073741824
2025/07/29-14:07:27.493840 43                   Options.log_file_time_to_roll: 0
2025/07/29-14:07:27.493840 43                       Options.keep_log_file_num: 1000
2025/07/29-14:07:27.493841 43                    Options.recycle_log_file_num: 0
2025/07/29-14:07:27.493841 43                         Options.allow_fallocate: 1
2025/07/29-14:07:27.493842 43                        Options.allow_mmap_reads: 0
2025/07/29-14:07:27.493842 43                       Options.allow_mmap_writes: 0
2025/07/29-14:07:27.493843 43                        Options.use_direct_reads: 0
2025/07/29-14:07:27.493843 43                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/29-14:07:27.493844 43          Options.create_missing_column_families: 1
2025/07/29-14:07:27.493844 43                              Options.db_log_dir: 
2025/07/29-14:07:27.493845 43                                 Options.wal_dir: 
2025/07/29-14:07:27.493845 43                Options.table_cache_numshardbits: 6
2025/07/29-14:07:27.493846 43                         Options.WAL_ttl_seconds: 0
2025/07/29-14:07:27.493847 43                       Options.WAL_size_limit_MB: 0
2025/07/29-14:07:27.493847 43                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/29-14:07:27.493848 43             Options.manifest_preallocation_size: 4194304
2025/07/29-14:07:27.493848 43                     Options.is_fd_close_on_exec: 1
2025/07/29-14:07:27.493849 43                   Options.advise_random_on_open: 1
2025/07/29-14:07:27.493849 43                   Options.experimental_mempurge_threshold: 0.000000
2025/07/29-14:07:27.493854 43                    Options.db_write_buffer_size: 0
2025/07/29-14:07:27.493855 43                    Options.write_buffer_manager: 0x7a41d3a40280
2025/07/29-14:07:27.493855 43         Options.access_hint_on_compaction_start: 1
2025/07/29-14:07:27.493856 43  Options.new_table_reader_for_compaction_inputs: 0
2025/07/29-14:07:27.493856 43           Options.random_access_max_buffer_size: 1048576
2025/07/29-14:07:27.493857 43                      Options.use_adaptive_mutex: 0
2025/07/29-14:07:27.493857 43                            Options.rate_limiter: (nil)
2025/07/29-14:07:27.493859 43     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/29-14:07:27.493860 43                       Options.wal_recovery_mode: 2
2025/07/29-14:07:27.494198 43                  Options.enable_thread_tracking: 0
2025/07/29-14:07:27.494204 43                  Options.enable_pipelined_write: 0
2025/07/29-14:07:27.494205 43                  Options.unordered_write: 0
2025/07/29-14:07:27.494206 43         Options.allow_concurrent_memtable_write: 1
2025/07/29-14:07:27.494206 43      Options.enable_write_thread_adaptive_yield: 1
2025/07/29-14:07:27.494207 43             Options.write_thread_max_yield_usec: 100
2025/07/29-14:07:27.494208 43            Options.write_thread_slow_yield_usec: 3
2025/07/29-14:07:27.494208 43                               Options.row_cache: None
2025/07/29-14:07:27.494209 43                              Options.wal_filter: None
2025/07/29-14:07:27.494209 43             Options.avoid_flush_during_recovery: 0
2025/07/29-14:07:27.494210 43             Options.allow_ingest_behind: 0
2025/07/29-14:07:27.494211 43             Options.preserve_deletes: 0
2025/07/29-14:07:27.494211 43             Options.two_write_queues: 0
2025/07/29-14:07:27.494212 43             Options.manual_wal_flush: 0
2025/07/29-14:07:27.494212 43             Options.atomic_flush: 0
2025/07/29-14:07:27.494213 43             Options.avoid_unnecessary_blocking_io: 0
2025/07/29-14:07:27.494213 43                 Options.persist_stats_to_disk: 0
2025/07/29-14:07:27.494214 43                 Options.write_dbid_to_manifest: 0
2025/07/29-14:07:27.494214 43                 Options.log_readahead_size: 0
2025/07/29-14:07:27.494215 43                 Options.file_checksum_gen_factory: Unknown
2025/07/29-14:07:27.494215 43                 Options.best_efforts_recovery: 0
2025/07/29-14:07:27.494216 43                Options.max_bgerror_resume_count: 2147483647
2025/07/29-14:07:27.494216 43            Options.bgerror_resume_retry_interval: 1000000
2025/07/29-14:07:27.494217 43             Options.allow_data_in_errors: 0
2025/07/29-14:07:27.494217 43             Options.db_host_id: __hostname__
2025/07/29-14:07:27.494219 43             Options.max_background_jobs: 2
2025/07/29-14:07:27.494219 43             Options.max_background_compactions: -1
2025/07/29-14:07:27.494220 43             Options.max_subcompactions: 1
2025/07/29-14:07:27.494220 43             Options.avoid_flush_during_shutdown: 0
2025/07/29-14:07:27.494221 43           Options.writable_file_max_buffer_size: 1048576
2025/07/29-14:07:27.494221 43             Options.delayed_write_rate : 16777216
2025/07/29-14:07:27.494222 43             Options.max_total_wal_size: 0
2025/07/29-14:07:27.494222 43             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/29-14:07:27.494223 43                   Options.stats_dump_period_sec: 600
2025/07/29-14:07:27.494223 43                 Options.stats_persist_period_sec: 600
2025/07/29-14:07:27.494224 43                 Options.stats_history_buffer_size: 1048576
2025/07/29-14:07:27.494225 43                          Options.max_open_files: -1
2025/07/29-14:07:27.494225 43                          Options.bytes_per_sync: 0
2025/07/29-14:07:27.494226 43                      Options.wal_bytes_per_sync: 0
2025/07/29-14:07:27.494226 43                   Options.strict_bytes_per_sync: 0
2025/07/29-14:07:27.494227 43       Options.compaction_readahead_size: 0
2025/07/29-14:07:27.494227 43                  Options.max_background_flushes: 1
2025/07/29-14:07:27.494228 43 Compression algorithms supported:
2025/07/29-14:07:27.494231 43 	kZSTD supported: 1
2025/07/29-14:07:27.494232 43 	kXpressCompression supported: 0
2025/07/29-14:07:27.494233 43 	kBZip2Compression supported: 0
2025/07/29-14:07:27.494234 43 	kZSTDNotFinalCompression supported: 1
2025/07/29-14:07:27.494235 43 	kLZ4Compression supported: 0
2025/07/29-14:07:27.494235 43 	kZlibCompression supported: 0
2025/07/29-14:07:27.494236 43 	kLZ4HCCompression supported: 0
2025/07/29-14:07:27.494236 43 	kSnappyCompression supported: 0
2025/07/29-14:07:27.494242 43 Fast CRC32 supported: Not supported on x86
2025/07/29-14:07:27.499723 43 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000011
2025/07/29-14:07:27.501335 43 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/07/29-14:07:27.501344 43               Options.comparator: leveldb.BytewiseComparator
2025/07/29-14:07:27.501346 43           Options.merge_operator: None
2025/07/29-14:07:27.501346 43        Options.compaction_filter: None
2025/07/29-14:07:27.501347 43        Options.compaction_filter_factory: None
2025/07/29-14:07:27.501347 43  Options.sst_partitioner_factory: None
2025/07/29-14:07:27.501348 43         Options.memtable_factory: SkipListFactory
2025/07/29-14:07:27.501349 43            Options.table_factory: BlockBasedTable
2025/07/29-14:07:27.501364 43            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7a41d3b00420)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7a41d3a40010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 988147875
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/29-14:07:27.501365 43        Options.write_buffer_size: 67108864
2025/07/29-14:07:27.501366 43  Options.max_write_buffer_number: 2
2025/07/29-14:07:27.501366 43        Options.compression[0]: NoCompression
2025/07/29-14:07:27.501367 43        Options.compression[1]: NoCompression
2025/07/29-14:07:27.501368 43        Options.compression[2]: ZSTD
2025/07/29-14:07:27.501368 43        Options.compression[3]: ZSTD
2025/07/29-14:07:27.501368 43        Options.compression[4]: ZSTD
2025/07/29-14:07:27.501369 43                  Options.bottommost_compression: Disabled
2025/07/29-14:07:27.501369 43       Options.prefix_extractor: nullptr
2025/07/29-14:07:27.501370 43   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/29-14:07:27.501370 43             Options.num_levels: 5
2025/07/29-14:07:27.501371 43        Options.min_write_buffer_number_to_merge: 1
2025/07/29-14:07:27.501371 43     Options.max_write_buffer_number_to_maintain: 0
2025/07/29-14:07:27.501372 43     Options.max_write_buffer_size_to_maintain: 0
2025/07/29-14:07:27.501372 43            Options.bottommost_compression_opts.window_bits: -14
2025/07/29-14:07:27.501373 43                  Options.bottommost_compression_opts.level: 32767
2025/07/29-14:07:27.501373 43               Options.bottommost_compression_opts.strategy: 0
2025/07/29-14:07:27.501373 43         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/29-14:07:27.501374 43         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/29-14:07:27.501374 43         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/29-14:07:27.501375 43                  Options.bottommost_compression_opts.enabled: false
2025/07/29-14:07:27.501375 43         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/29-14:07:27.501376 43            Options.compression_opts.window_bits: -14
2025/07/29-14:07:27.501376 43                  Options.compression_opts.level: 32767
2025/07/29-14:07:27.501377 43               Options.compression_opts.strategy: 0
2025/07/29-14:07:27.501377 43         Options.compression_opts.max_dict_bytes: 0
2025/07/29-14:07:27.501378 43         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/29-14:07:27.501378 43         Options.compression_opts.parallel_threads: 1
2025/07/29-14:07:27.501509 43                  Options.compression_opts.enabled: false
2025/07/29-14:07:27.501513 43         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/29-14:07:27.501514 43      Options.level0_file_num_compaction_trigger: 4
2025/07/29-14:07:27.501515 43          Options.level0_slowdown_writes_trigger: 20
2025/07/29-14:07:27.501515 43              Options.level0_stop_writes_trigger: 36
2025/07/29-14:07:27.501516 43                   Options.target_file_size_base: 67108864
2025/07/29-14:07:27.501516 43             Options.target_file_size_multiplier: 2
2025/07/29-14:07:27.501517 43                Options.max_bytes_for_level_base: 268435456
2025/07/29-14:07:27.501517 43 Options.level_compaction_dynamic_level_bytes: 0
2025/07/29-14:07:27.501518 43          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/29-14:07:27.501520 43 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/29-14:07:27.501521 43 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/29-14:07:27.501521 43 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/29-14:07:27.501522 43 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/29-14:07:27.501522 43 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/29-14:07:27.501523 43 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/29-14:07:27.501523 43 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/29-14:07:27.501524 43       Options.max_sequential_skip_in_iterations: 8
2025/07/29-14:07:27.501524 43                    Options.max_compaction_bytes: 1677721600
2025/07/29-14:07:27.501528 43                        Options.arena_block_size: 1048576
2025/07/29-14:07:27.501529 43   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/29-14:07:27.501529 43   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/29-14:07:27.501530 43       Options.rate_limit_delay_max_milliseconds: 100
2025/07/29-14:07:27.501530 43                Options.disable_auto_compactions: 0
2025/07/29-14:07:27.501534 43                        Options.compaction_style: kCompactionStyleLevel
2025/07/29-14:07:27.501536 43                          Options.compaction_pri: kMinOverlappingRatio
2025/07/29-14:07:27.501536 43 Options.compaction_options_universal.size_ratio: 1
2025/07/29-14:07:27.501536 43 Options.compaction_options_universal.min_merge_width: 2
2025/07/29-14:07:27.501537 43 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/29-14:07:27.501537 43 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/29-14:07:27.501538 43 Options.compaction_options_universal.compression_size_percent: -1
2025/07/29-14:07:27.501539 43 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/29-14:07:27.501539 43 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/29-14:07:27.501540 43 Options.compaction_options_fifo.allow_compaction: 0
2025/07/29-14:07:27.501547 43                   Options.table_properties_collectors: 
2025/07/29-14:07:27.501547 43                   Options.inplace_update_support: 0
2025/07/29-14:07:27.501548 43                 Options.inplace_update_num_locks: 10000
2025/07/29-14:07:27.501548 43               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/29-14:07:27.501549 43               Options.memtable_whole_key_filtering: 0
2025/07/29-14:07:27.501549 43   Options.memtable_huge_page_size: 0
2025/07/29-14:07:27.501550 43                           Options.bloom_locality: 0
2025/07/29-14:07:27.501550 43                    Options.max_successive_merges: 0
2025/07/29-14:07:27.501551 43                Options.optimize_filters_for_hits: 0
2025/07/29-14:07:27.501551 43                Options.paranoid_file_checks: 0
2025/07/29-14:07:27.501552 43                Options.force_consistency_checks: 1
2025/07/29-14:07:27.501552 43                Options.report_bg_io_stats: 0
2025/07/29-14:07:27.501552 43                               Options.ttl: 2592000
2025/07/29-14:07:27.501553 43          Options.periodic_compaction_seconds: 0
2025/07/29-14:07:27.501714 43                       Options.enable_blob_files: false
2025/07/29-14:07:27.501716 43                           Options.min_blob_size: 0
2025/07/29-14:07:27.501717 43                          Options.blob_file_size: 268435456
2025/07/29-14:07:27.501718 43                   Options.blob_compression_type: NoCompression
2025/07/29-14:07:27.501718 43          Options.enable_blob_garbage_collection: false
2025/07/29-14:07:27.501719 43      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/29-14:07:27.501720 43 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/29-14:07:27.501720 43          Options.blob_compaction_readahead_size: 0
2025/07/29-14:07:27.502550 43 [db/column_family.cc:605] --------------- Options for column family [properties]:
2025/07/29-14:07:27.502557 43               Options.comparator: leveldb.BytewiseComparator
2025/07/29-14:07:27.502558 43           Options.merge_operator: None
2025/07/29-14:07:27.502558 43        Options.compaction_filter: None
2025/07/29-14:07:27.502559 43        Options.compaction_filter_factory: None
2025/07/29-14:07:27.502559 43  Options.sst_partitioner_factory: None
2025/07/29-14:07:27.502560 43         Options.memtable_factory: SkipListFactory
2025/07/29-14:07:27.502560 43            Options.table_factory: BlockBasedTable
2025/07/29-14:07:27.502571 43            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7a41d3b00420)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7a41d3a40010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 988147875
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/29-14:07:27.502572 43        Options.write_buffer_size: 67108864
2025/07/29-14:07:27.502572 43  Options.max_write_buffer_number: 2
2025/07/29-14:07:27.502573 43        Options.compression[0]: NoCompression
2025/07/29-14:07:27.502574 43        Options.compression[1]: NoCompression
2025/07/29-14:07:27.502574 43        Options.compression[2]: ZSTD
2025/07/29-14:07:27.502575 43        Options.compression[3]: ZSTD
2025/07/29-14:07:27.502575 43        Options.compression[4]: ZSTD
2025/07/29-14:07:27.502576 43                  Options.bottommost_compression: Disabled
2025/07/29-14:07:27.502576 43       Options.prefix_extractor: nullptr
2025/07/29-14:07:27.502577 43   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/29-14:07:27.502577 43             Options.num_levels: 5
2025/07/29-14:07:27.502577 43        Options.min_write_buffer_number_to_merge: 1
2025/07/29-14:07:27.502578 43     Options.max_write_buffer_number_to_maintain: 0
2025/07/29-14:07:27.502578 43     Options.max_write_buffer_size_to_maintain: 0
2025/07/29-14:07:27.502579 43            Options.bottommost_compression_opts.window_bits: -14
2025/07/29-14:07:27.502579 43                  Options.bottommost_compression_opts.level: 32767
2025/07/29-14:07:27.502580 43               Options.bottommost_compression_opts.strategy: 0
2025/07/29-14:07:27.502580 43         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/29-14:07:27.502581 43         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/29-14:07:27.502581 43         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/29-14:07:27.502748 43                  Options.bottommost_compression_opts.enabled: false
2025/07/29-14:07:27.502750 43         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/29-14:07:27.502751 43            Options.compression_opts.window_bits: -14
2025/07/29-14:07:27.502752 43                  Options.compression_opts.level: 32767
2025/07/29-14:07:27.502752 43               Options.compression_opts.strategy: 0
2025/07/29-14:07:27.502753 43         Options.compression_opts.max_dict_bytes: 0
2025/07/29-14:07:27.502753 43         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/29-14:07:27.502754 43         Options.compression_opts.parallel_threads: 1
2025/07/29-14:07:27.502754 43                  Options.compression_opts.enabled: false
2025/07/29-14:07:27.502755 43         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/29-14:07:27.502755 43      Options.level0_file_num_compaction_trigger: 4
2025/07/29-14:07:27.502756 43          Options.level0_slowdown_writes_trigger: 20
2025/07/29-14:07:27.502756 43              Options.level0_stop_writes_trigger: 36
2025/07/29-14:07:27.502756 43                   Options.target_file_size_base: 67108864
2025/07/29-14:07:27.502757 43             Options.target_file_size_multiplier: 2
2025/07/29-14:07:27.502757 43                Options.max_bytes_for_level_base: 268435456
2025/07/29-14:07:27.502758 43 Options.level_compaction_dynamic_level_bytes: 0
2025/07/29-14:07:27.502758 43          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/29-14:07:27.502760 43 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/29-14:07:27.502760 43 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/29-14:07:27.502761 43 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/29-14:07:27.502761 43 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/29-14:07:27.502762 43 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/29-14:07:27.502762 43 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/29-14:07:27.502762 43 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/29-14:07:27.502763 43       Options.max_sequential_skip_in_iterations: 8
2025/07/29-14:07:27.502763 43                    Options.max_compaction_bytes: 1677721600
2025/07/29-14:07:27.502764 43                        Options.arena_block_size: 1048576
2025/07/29-14:07:27.502764 43   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/29-14:07:27.502765 43   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/29-14:07:27.502765 43       Options.rate_limit_delay_max_milliseconds: 100
2025/07/29-14:07:27.502766 43                Options.disable_auto_compactions: 0
2025/07/29-14:07:27.502769 43                        Options.compaction_style: kCompactionStyleLevel
2025/07/29-14:07:27.502769 43                          Options.compaction_pri: kMinOverlappingRatio
2025/07/29-14:07:27.502770 43 Options.compaction_options_universal.size_ratio: 1
2025/07/29-14:07:27.502770 43 Options.compaction_options_universal.min_merge_width: 2
2025/07/29-14:07:27.502770 43 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/29-14:07:27.502771 43 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/29-14:07:27.502771 43 Options.compaction_options_universal.compression_size_percent: -1
2025/07/29-14:07:27.502772 43 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/29-14:07:27.502773 43 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/29-14:07:27.502773 43 Options.compaction_options_fifo.allow_compaction: 0
2025/07/29-14:07:27.502777 43                   Options.table_properties_collectors: 
2025/07/29-14:07:27.502777 43                   Options.inplace_update_support: 0
2025/07/29-14:07:27.502778 43                 Options.inplace_update_num_locks: 10000
2025/07/29-14:07:27.502778 43               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/29-14:07:27.502779 43               Options.memtable_whole_key_filtering: 0
2025/07/29-14:07:27.503004 43   Options.memtable_huge_page_size: 0
2025/07/29-14:07:27.503008 43                           Options.bloom_locality: 0
2025/07/29-14:07:27.503009 43                    Options.max_successive_merges: 0
2025/07/29-14:07:27.503009 43                Options.optimize_filters_for_hits: 0
2025/07/29-14:07:27.503010 43                Options.paranoid_file_checks: 0
2025/07/29-14:07:27.503010 43                Options.force_consistency_checks: 1
2025/07/29-14:07:27.503011 43                Options.report_bg_io_stats: 0
2025/07/29-14:07:27.503012 43                               Options.ttl: 2592000
2025/07/29-14:07:27.503012 43          Options.periodic_compaction_seconds: 0
2025/07/29-14:07:27.503013 43                       Options.enable_blob_files: false
2025/07/29-14:07:27.503013 43                           Options.min_blob_size: 0
2025/07/29-14:07:27.503014 43                          Options.blob_file_size: 268435456
2025/07/29-14:07:27.503015 43                   Options.blob_compression_type: NoCompression
2025/07/29-14:07:27.503015 43          Options.enable_blob_garbage_collection: false
2025/07/29-14:07:27.503016 43      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/29-14:07:27.503018 43 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/29-14:07:27.503019 43          Options.blob_compaction_readahead_size: 0
2025/07/29-14:07:27.507581 43 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000011 succeeded,manifest_file_number is 11, next_file_number is 13, last_sequence is 0, log_number is 6,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 0
2025/07/29-14:07:27.507592 43 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 6
2025/07/29-14:07:27.507594 43 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 6
2025/07/29-14:07:27.511858 43 [db/version_set.cc:4409] Creating manifest 15
2025/07/29-14:07:27.533178 43 EVENT_LOG_v1 {"time_micros": 1753798047533166, "job": 1, "event": "recovery_started", "wal_files": [5, 12]}
2025/07/29-14:07:27.533186 43 [db/db_impl/db_impl_open.cc:874] Skipping log #5 since it is older than min log to keep #6
2025/07/29-14:07:27.533189 43 [db/db_impl/db_impl_open.cc:888] Recovering log #12 mode 2
2025/07/29-14:07:27.534841 43 [db/version_set.cc:4409] Creating manifest 16
2025/07/29-14:07:27.563288 43 EVENT_LOG_v1 {"time_micros": 1753798047563276, "job": 1, "event": "recovery_finished"}
2025/07/29-14:07:27.583877 43 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7a41d1190700
2025/07/29-14:07:27.584885 43 DB pointer 0x7a41d1021c00
