// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"github.com/coze-dev/coze-studio/backend/api/model/crossdomain/plugin"
	"gorm.io/gorm"
)

const TableNameToolVersion = "tool_version"

// ToolVersion Tool Version
type ToolVersion struct {
	ID        int64                     `gorm:"column:id;primaryKey;comment:Primary Key ID" json:"id"`                                                 // Primary Key ID
	ToolID    int64                     `gorm:"column:tool_id;not null;comment:Tool ID" json:"tool_id"`                                                // Tool ID
	PluginID  int64                     `gorm:"column:plugin_id;not null;comment:Plugin ID" json:"plugin_id"`                                          // Plugin ID
	Version   string                    `gorm:"column:version;not null;comment:Tool Version, e.g. v1.0.0" json:"version"`                              // Tool Version, e.g. v1.0.0
	SubURL    string                    `gorm:"column:sub_url;not null;comment:Sub URL Path" json:"sub_url"`                                           // Sub URL Path
	Method    string                    `gorm:"column:method;not null;comment:HTTP Request Method" json:"method"`                                      // HTTP Request Method
	Operation *plugin.Openapi3Operation `gorm:"column:operation;comment:Tool Openapi Operation Schema;serializer:json" json:"operation"`               // Tool Openapi Operation Schema
	CreatedAt int64                     `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	DeletedAt gorm.DeletedAt            `gorm:"column:deleted_at;comment:Delete Time" json:"deleted_at"`                                               // Delete Time
}

// TableName ToolVersion's table name
func (*ToolVersion) TableName() string {
	return TableNameToolVersion
}
