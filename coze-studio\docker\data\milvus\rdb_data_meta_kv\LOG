2025/07/29-14:07:27.373693 43 RocksDB version: 6.29.5
2025/07/29-14:07:27.374203 43 Git sha 0
2025/07/29-14:07:27.374207 43 Compile date 2024-11-15 11:22:58
2025/07/29-14:07:27.374214 43 DB SUMMARY
2025/07/29-14:07:27.374215 43 DB Session ID:  ITIJJB5VQ9YSIJ2X9MH8
2025/07/29-14:07:27.376915 43 CURRENT file:  CURRENT
2025/07/29-14:07:27.376931 43 IDENTITY file:  IDENTITY
2025/07/29-14:07:27.377584 43 MANIFEST file:  MANIFEST-000010 size: 185 Bytes
2025/07/29-14:07:27.377598 43 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 1, files: 000009.sst 
2025/07/29-14:07:27.377606 43 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000011.log size: 0 ; 
2025/07/29-14:07:27.377612 43                         Options.error_if_exists: 0
2025/07/29-14:07:27.377613 43                       Options.create_if_missing: 1
2025/07/29-14:07:27.377613 43                         Options.paranoid_checks: 1
2025/07/29-14:07:27.377614 43             Options.flush_verify_memtable_count: 1
2025/07/29-14:07:27.377614 43                               Options.track_and_verify_wals_in_manifest: 0
2025/07/29-14:07:27.377615 43                                     Options.env: 0x7a42ae10cd00
2025/07/29-14:07:27.377616 43                                      Options.fs: PosixFileSystem
2025/07/29-14:07:27.377617 43                                Options.info_log: 0x7a41d1090050
2025/07/29-14:07:27.377618 43                Options.max_file_opening_threads: 16
2025/07/29-14:07:27.377618 43                              Options.statistics: (nil)
2025/07/29-14:07:27.377619 43                               Options.use_fsync: 0
2025/07/29-14:07:27.377619 43                       Options.max_log_file_size: 0
2025/07/29-14:07:27.377620 43                  Options.max_manifest_file_size: 1073741824
2025/07/29-14:07:27.377620 43                   Options.log_file_time_to_roll: 0
2025/07/29-14:07:27.377621 43                       Options.keep_log_file_num: 1000
2025/07/29-14:07:27.377621 43                    Options.recycle_log_file_num: 0
2025/07/29-14:07:27.377622 43                         Options.allow_fallocate: 1
2025/07/29-14:07:27.377622 43                        Options.allow_mmap_reads: 0
2025/07/29-14:07:27.377623 43                       Options.allow_mmap_writes: 0
2025/07/29-14:07:27.377623 43                        Options.use_direct_reads: 0
2025/07/29-14:07:27.377624 43                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/29-14:07:27.377624 43          Options.create_missing_column_families: 0
2025/07/29-14:07:27.377625 43                              Options.db_log_dir: 
2025/07/29-14:07:27.377625 43                                 Options.wal_dir: 
2025/07/29-14:07:27.377626 43                Options.table_cache_numshardbits: 6
2025/07/29-14:07:27.377626 43                         Options.WAL_ttl_seconds: 0
2025/07/29-14:07:27.377627 43                       Options.WAL_size_limit_MB: 0
2025/07/29-14:07:27.377627 43                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/29-14:07:27.377628 43             Options.manifest_preallocation_size: 4194304
2025/07/29-14:07:27.377628 43                     Options.is_fd_close_on_exec: 1
2025/07/29-14:07:27.377629 43                   Options.advise_random_on_open: 1
2025/07/29-14:07:27.377629 43                   Options.experimental_mempurge_threshold: 0.000000
2025/07/29-14:07:27.377646 43                    Options.db_write_buffer_size: 0
2025/07/29-14:07:27.377646 43                    Options.write_buffer_manager: 0x7a41d3a400a0
2025/07/29-14:07:27.377647 43         Options.access_hint_on_compaction_start: 1
2025/07/29-14:07:27.377647 43  Options.new_table_reader_for_compaction_inputs: 0
2025/07/29-14:07:27.377648 43           Options.random_access_max_buffer_size: 1048576
2025/07/29-14:07:27.377648 43                      Options.use_adaptive_mutex: 0
2025/07/29-14:07:27.377649 43                            Options.rate_limiter: (nil)
2025/07/29-14:07:27.377653 43     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/29-14:07:27.377654 43                       Options.wal_recovery_mode: 2
2025/07/29-14:07:27.377934 43                  Options.enable_thread_tracking: 0
2025/07/29-14:07:27.377939 43                  Options.enable_pipelined_write: 0
2025/07/29-14:07:27.377940 43                  Options.unordered_write: 0
2025/07/29-14:07:27.377941 43         Options.allow_concurrent_memtable_write: 1
2025/07/29-14:07:27.377941 43      Options.enable_write_thread_adaptive_yield: 1
2025/07/29-14:07:27.377942 43             Options.write_thread_max_yield_usec: 100
2025/07/29-14:07:27.377942 43            Options.write_thread_slow_yield_usec: 3
2025/07/29-14:07:27.377943 43                               Options.row_cache: None
2025/07/29-14:07:27.377944 43                              Options.wal_filter: None
2025/07/29-14:07:27.377944 43             Options.avoid_flush_during_recovery: 0
2025/07/29-14:07:27.377945 43             Options.allow_ingest_behind: 0
2025/07/29-14:07:27.377946 43             Options.preserve_deletes: 0
2025/07/29-14:07:27.377946 43             Options.two_write_queues: 0
2025/07/29-14:07:27.377947 43             Options.manual_wal_flush: 0
2025/07/29-14:07:27.377947 43             Options.atomic_flush: 0
2025/07/29-14:07:27.377948 43             Options.avoid_unnecessary_blocking_io: 0
2025/07/29-14:07:27.377948 43                 Options.persist_stats_to_disk: 0
2025/07/29-14:07:27.377949 43                 Options.write_dbid_to_manifest: 0
2025/07/29-14:07:27.377949 43                 Options.log_readahead_size: 0
2025/07/29-14:07:27.377950 43                 Options.file_checksum_gen_factory: Unknown
2025/07/29-14:07:27.377950 43                 Options.best_efforts_recovery: 0
2025/07/29-14:07:27.377951 43                Options.max_bgerror_resume_count: 2147483647
2025/07/29-14:07:27.377951 43            Options.bgerror_resume_retry_interval: 1000000
2025/07/29-14:07:27.377952 43             Options.allow_data_in_errors: 0
2025/07/29-14:07:27.377952 43             Options.db_host_id: __hostname__
2025/07/29-14:07:27.377956 43             Options.max_background_jobs: 2
2025/07/29-14:07:27.377957 43             Options.max_background_compactions: -1
2025/07/29-14:07:27.377957 43             Options.max_subcompactions: 1
2025/07/29-14:07:27.377958 43             Options.avoid_flush_during_shutdown: 0
2025/07/29-14:07:27.377958 43           Options.writable_file_max_buffer_size: 1048576
2025/07/29-14:07:27.377959 43             Options.delayed_write_rate : 16777216
2025/07/29-14:07:27.377959 43             Options.max_total_wal_size: 0
2025/07/29-14:07:27.377960 43             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/29-14:07:27.377961 43                   Options.stats_dump_period_sec: 600
2025/07/29-14:07:27.377961 43                 Options.stats_persist_period_sec: 600
2025/07/29-14:07:27.377962 43                 Options.stats_history_buffer_size: 1048576
2025/07/29-14:07:27.377962 43                          Options.max_open_files: -1
2025/07/29-14:07:27.377963 43                          Options.bytes_per_sync: 0
2025/07/29-14:07:27.377963 43                      Options.wal_bytes_per_sync: 0
2025/07/29-14:07:27.377964 43                   Options.strict_bytes_per_sync: 0
2025/07/29-14:07:27.377964 43       Options.compaction_readahead_size: 0
2025/07/29-14:07:27.377965 43                  Options.max_background_flushes: 1
2025/07/29-14:07:27.377965 43 Compression algorithms supported:
2025/07/29-14:07:27.377968 43 	kZSTD supported: 1
2025/07/29-14:07:27.377969 43 	kXpressCompression supported: 0
2025/07/29-14:07:27.377970 43 	kBZip2Compression supported: 0
2025/07/29-14:07:27.377971 43 	kZSTDNotFinalCompression supported: 1
2025/07/29-14:07:27.377971 43 	kLZ4Compression supported: 0
2025/07/29-14:07:27.377972 43 	kZlibCompression supported: 0
2025/07/29-14:07:27.377973 43 	kLZ4HCCompression supported: 0
2025/07/29-14:07:27.377973 43 	kSnappyCompression supported: 0
2025/07/29-14:07:27.377979 43 Fast CRC32 supported: Not supported on x86
2025/07/29-14:07:27.384869 43 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000010
2025/07/29-14:07:27.386663 43 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/07/29-14:07:27.386672 43               Options.comparator: leveldb.BytewiseComparator
2025/07/29-14:07:27.386674 43           Options.merge_operator: None
2025/07/29-14:07:27.386674 43        Options.compaction_filter: None
2025/07/29-14:07:27.386675 43        Options.compaction_filter_factory: None
2025/07/29-14:07:27.386675 43  Options.sst_partitioner_factory: None
2025/07/29-14:07:27.386676 43         Options.memtable_factory: SkipListFactory
2025/07/29-14:07:27.386677 43            Options.table_factory: BlockBasedTable
2025/07/29-14:07:27.386711 43            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7a41d3b000c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7a41d3a40010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 988147875
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/29-14:07:27.386713 43        Options.write_buffer_size: 67108864
2025/07/29-14:07:27.386713 43  Options.max_write_buffer_number: 2
2025/07/29-14:07:27.386715 43        Options.compression[0]: NoCompression
2025/07/29-14:07:27.386716 43        Options.compression[1]: NoCompression
2025/07/29-14:07:27.386716 43        Options.compression[2]: ZSTD
2025/07/29-14:07:27.386717 43        Options.compression[3]: ZSTD
2025/07/29-14:07:27.386717 43        Options.compression[4]: ZSTD
2025/07/29-14:07:27.386718 43                  Options.bottommost_compression: Disabled
2025/07/29-14:07:27.386718 43       Options.prefix_extractor: nullptr
2025/07/29-14:07:27.386719 43   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/29-14:07:27.386719 43             Options.num_levels: 5
2025/07/29-14:07:27.386720 43        Options.min_write_buffer_number_to_merge: 1
2025/07/29-14:07:27.386720 43     Options.max_write_buffer_number_to_maintain: 0
2025/07/29-14:07:27.386720 43     Options.max_write_buffer_size_to_maintain: 0
2025/07/29-14:07:27.386721 43            Options.bottommost_compression_opts.window_bits: -14
2025/07/29-14:07:27.386721 43                  Options.bottommost_compression_opts.level: 32767
2025/07/29-14:07:27.386722 43               Options.bottommost_compression_opts.strategy: 0
2025/07/29-14:07:27.386722 43         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/29-14:07:27.386723 43         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/29-14:07:27.386723 43         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/29-14:07:27.386724 43                  Options.bottommost_compression_opts.enabled: false
2025/07/29-14:07:27.386724 43         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/29-14:07:27.386744 43            Options.compression_opts.window_bits: -14
2025/07/29-14:07:27.386745 43                  Options.compression_opts.level: 32767
2025/07/29-14:07:27.386746 43               Options.compression_opts.strategy: 0
2025/07/29-14:07:27.386747 43         Options.compression_opts.max_dict_bytes: 0
2025/07/29-14:07:27.386747 43         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/29-14:07:27.386747 43         Options.compression_opts.parallel_threads: 1
2025/07/29-14:07:27.386901 43                  Options.compression_opts.enabled: false
2025/07/29-14:07:27.386905 43         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/29-14:07:27.386906 43      Options.level0_file_num_compaction_trigger: 4
2025/07/29-14:07:27.386906 43          Options.level0_slowdown_writes_trigger: 20
2025/07/29-14:07:27.386907 43              Options.level0_stop_writes_trigger: 36
2025/07/29-14:07:27.386907 43                   Options.target_file_size_base: 67108864
2025/07/29-14:07:27.386908 43             Options.target_file_size_multiplier: 2
2025/07/29-14:07:27.386908 43                Options.max_bytes_for_level_base: 268435456
2025/07/29-14:07:27.386909 43 Options.level_compaction_dynamic_level_bytes: 0
2025/07/29-14:07:27.386909 43          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/29-14:07:27.386912 43 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/29-14:07:27.386912 43 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/29-14:07:27.386913 43 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/29-14:07:27.386913 43 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/29-14:07:27.386914 43 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/29-14:07:27.386914 43 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/29-14:07:27.386915 43 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/29-14:07:27.386915 43       Options.max_sequential_skip_in_iterations: 8
2025/07/29-14:07:27.386915 43                    Options.max_compaction_bytes: 1677721600
2025/07/29-14:07:27.386916 43                        Options.arena_block_size: 1048576
2025/07/29-14:07:27.386916 43   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/29-14:07:27.386917 43   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/29-14:07:27.386917 43       Options.rate_limit_delay_max_milliseconds: 100
2025/07/29-14:07:27.386918 43                Options.disable_auto_compactions: 0
2025/07/29-14:07:27.386921 43                        Options.compaction_style: kCompactionStyleLevel
2025/07/29-14:07:27.386922 43                          Options.compaction_pri: kMinOverlappingRatio
2025/07/29-14:07:27.386923 43 Options.compaction_options_universal.size_ratio: 1
2025/07/29-14:07:27.386923 43 Options.compaction_options_universal.min_merge_width: 2
2025/07/29-14:07:27.386924 43 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/29-14:07:27.386924 43 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/29-14:07:27.386925 43 Options.compaction_options_universal.compression_size_percent: -1
2025/07/29-14:07:27.386925 43 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/29-14:07:27.386926 43 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/29-14:07:27.386926 43 Options.compaction_options_fifo.allow_compaction: 0
2025/07/29-14:07:27.386933 43                   Options.table_properties_collectors: 
2025/07/29-14:07:27.386933 43                   Options.inplace_update_support: 0
2025/07/29-14:07:27.386934 43                 Options.inplace_update_num_locks: 10000
2025/07/29-14:07:27.386934 43               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/29-14:07:27.386935 43               Options.memtable_whole_key_filtering: 0
2025/07/29-14:07:27.386935 43   Options.memtable_huge_page_size: 0
2025/07/29-14:07:27.386936 43                           Options.bloom_locality: 0
2025/07/29-14:07:27.386936 43                    Options.max_successive_merges: 0
2025/07/29-14:07:27.386937 43                Options.optimize_filters_for_hits: 0
2025/07/29-14:07:27.386937 43                Options.paranoid_file_checks: 0
2025/07/29-14:07:27.386938 43                Options.force_consistency_checks: 1
2025/07/29-14:07:27.386938 43                Options.report_bg_io_stats: 0
2025/07/29-14:07:27.386939 43                               Options.ttl: 2592000
2025/07/29-14:07:27.386939 43          Options.periodic_compaction_seconds: 0
2025/07/29-14:07:27.387188 43                       Options.enable_blob_files: false
2025/07/29-14:07:27.387191 43                           Options.min_blob_size: 0
2025/07/29-14:07:27.387192 43                          Options.blob_file_size: 268435456
2025/07/29-14:07:27.387193 43                   Options.blob_compression_type: NoCompression
2025/07/29-14:07:27.387194 43          Options.enable_blob_garbage_collection: false
2025/07/29-14:07:27.387194 43      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/29-14:07:27.387196 43 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/29-14:07:27.387197 43          Options.blob_compaction_readahead_size: 0
2025/07/29-14:07:27.391714 43 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000010 succeeded,manifest_file_number is 10, next_file_number is 12, last_sequence is 34, log_number is 6,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/29-14:07:27.391724 43 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 6
2025/07/29-14:07:27.396226 43 [db/version_set.cc:4409] Creating manifest 14
2025/07/29-14:07:27.419249 43 EVENT_LOG_v1 {"time_micros": 1753798047419228, "job": 1, "event": "recovery_started", "wal_files": [11]}
2025/07/29-14:07:27.419259 43 [db/db_impl/db_impl_open.cc:888] Recovering log #11 mode 2
2025/07/29-14:07:27.420525 43 [db/version_set.cc:4409] Creating manifest 15
2025/07/29-14:07:27.443713 43 EVENT_LOG_v1 {"time_micros": 1753798047443697, "job": 1, "event": "recovery_finished"}
2025/07/29-14:07:27.477912 43 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7a41d1190000
2025/07/29-14:07:27.479602 43 DB pointer 0x7a41d1020000
2025/07/29-14:07:27.480171 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-14:07:27.480187 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.69 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      1/0    1.69 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7a41d3a40010#8 capacity: 942.37 MB collections: 1 last_copies: 0 last_secs: 6.3e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
