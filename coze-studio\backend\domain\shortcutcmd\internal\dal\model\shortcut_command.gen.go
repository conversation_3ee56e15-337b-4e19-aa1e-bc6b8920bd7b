// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "github.com/coze-dev/coze-studio/backend/api/model/ocean/cloud/playground"

const TableNameShortcutCommand = "shortcut_command"

// ShortcutCommand bot快捷指令表
type ShortcutCommand struct {
	ID              int64                        `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                 // 主键ID
	ObjectID        int64                        `gorm:"column:object_id;not null;comment:实体ID,该实体可用这个指令" json:"object_id"`              // 实体ID,该实体可用这个指令
	CommandID       int64                        `gorm:"column:command_id;not null;comment:命令ID" json:"command_id"`                      // 命令ID
	CommandName     string                       `gorm:"column:command_name;not null;comment:命令名称" json:"command_name"`                  // 命令名称
	ShortcutCommand string                       `gorm:"column:shortcut_command;not null;comment:快捷指令" json:"shortcut_command"`          // 快捷指令
	Description     string                       `gorm:"column:description;not null;comment:命令描述" json:"description"`                    // 命令描述
	SendType        int32                        `gorm:"column:send_type;not null;comment:发送类型 0:query 1:panel" json:"send_type"`        // 发送类型 0:query 1:panel
	ToolType        int32                        `gorm:"column:tool_type;not null;comment:使用工具的type 1:workFlow 2:插件" json:"tool_type"`   // 使用工具的type 1:workFlow 2:插件
	WorkFlowID      int64                        `gorm:"column:work_flow_id;not null;comment:使用workFlow的id" json:"work_flow_id"`         // 使用workFlow的id
	PluginID        int64                        `gorm:"column:plugin_id;not null;comment:使用插件的id" json:"plugin_id"`                     // 使用插件的id
	PluginToolName  string                       `gorm:"column:plugin_tool_name;not null;comment:使用插件的api_name" json:"plugin_tool_name"` // 使用插件的api_name
	TemplateQuery   string                       `gorm:"column:template_query;comment:query模板" json:"template_query"`                    // query模板
	Components      []*playground.Components     `gorm:"column:components;comment:panel参数;serializer:json" json:"components"`            // panel参数
	CardSchema      string                       `gorm:"column:card_schema;comment:卡片schema" json:"card_schema"`                         // 卡片schema
	ToolInfo        *playground.ToolInfo         `gorm:"column:tool_info;comment:工具信息 包含name+变量列表;serializer:json" json:"tool_info"`     // 工具信息 包含name+变量列表
	Status          int32                        `gorm:"column:status;not null;comment:状态,0无效,1有效" json:"status"`                        // 状态,0无效,1有效
	CreatorID       int64                        `gorm:"column:creator_id;comment:创建者ID" json:"creator_id"`                              // 创建者ID
	IsOnline        int32                        `gorm:"column:is_online;not null;comment:是否为线上信息 0草稿 1线上" json:"is_online"`             // 是否为线上信息 0草稿 1线上
	CreatedAt       int64                        `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt       int64                        `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间" json:"updated_at"` // 更新时间
	AgentID         int64                        `gorm:"column:agent_id;not null;comment:multi的指令时，该指令由哪个节点执行" json:"agent_id"`          // multi的指令时，该指令由哪个节点执行
	ShortcutIcon    *playground.ShortcutFileInfo `gorm:"column:shortcut_icon;comment:快捷指令图标;serializer:json" json:"shortcut_icon"`       // 快捷指令图标
	PluginToolID    int64                        `gorm:"column:plugin_tool_id;not null;comment:tool_id" json:"plugin_tool_id"`           // tool_id
}

// TableName ShortcutCommand's table name
func (*ShortcutCommand) TableName() string {
	return TableNameShortcutCommand
}
