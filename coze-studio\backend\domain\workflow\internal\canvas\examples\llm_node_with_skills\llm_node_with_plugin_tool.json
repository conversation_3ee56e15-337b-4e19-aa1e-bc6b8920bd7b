{"nodes": [{"id": "100001", "type": "1", "meta": {"position": {"x": -236, "y": -434}}, "data": {"outputs": [{"type": "string", "name": "e", "required": true}], "nodeMeta": {"title": "开始", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "description": "工作流的起始节点，用于设定启动工作流需要的信息", "mainColor": "#5C62FF", "subTitle": ""}, "trigger_parameters": [{"type": "string", "name": "e", "required": true}]}}, {"id": "900001", "type": "2", "meta": {"position": {"x": 712, "y": -434}}, "data": {"nodeMeta": {"title": "结束", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "description": "工作流的最终节点，用于返回工作流运行后的结果信息", "mainColor": "#5C62FF", "subTitle": ""}, "inputs": {"terminatePlan": "returnVariables", "inputParameters": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "e"}, "rawMeta": {"type": 1}}}}]}}}, {"id": "120238", "type": "3", "meta": {"position": {"x": 219, "y": -513.4}}, "data": {"nodeMeta": {"title": "大模型", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "description": "调用大语言模型,使用变量和提示词生成回复", "mainColor": "#5C62FF", "subTitle": "大模型"}, "inputs": {"inputParameters": [{"name": "input", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "e"}, "rawMeta": {"type": 1}}}}], "llmParam": [{"name": "modelType", "input": {"type": "integer", "value": {"type": "literal", "content": "1", "rawMeta": {"type": 2}}}}, {"name": "modleName", "input": {"type": "string", "value": {"type": "literal", "content": "豆包·1.5·Pro·32k", "rawMeta": {"type": 1}}}}, {"name": "generationDiversity", "input": {"type": "string", "value": {"type": "literal", "content": "balance", "rawMeta": {"type": 1}}}}, {"name": "temperature", "input": {"type": "float", "value": {"type": "literal", "content": "0.8", "rawMeta": {"type": 4}}}}, {"name": "maxTokens", "input": {"type": "integer", "value": {"type": "literal", "content": "4096", "rawMeta": {"type": 2}}}}, {"name": "responseFormat", "input": {"type": "integer", "value": {"type": "literal", "content": "2", "rawMeta": {"type": 2}}}}, {"name": "prompt", "input": {"type": "string", "value": {"type": "literal", "content": "{{input}}", "rawMeta": {"type": 1}}}}, {"name": "enableChatHistory", "input": {"type": "boolean", "value": {"type": "literal", "content": false, "rawMeta": {"type": 3}}}}, {"name": "chatHistoryRound", "input": {"type": "integer", "value": {"type": "literal", "content": "3", "rawMeta": {"type": 2}}}}, {"name": "systemPrompt", "input": {"type": "string", "value": {"type": "literal", "content": "你是一个占卜师", "rawMeta": {"type": 1}}}}], "fcParam": {"pluginFCParam": {"pluginList": [{"plugin_id": "7509353177339133952", "api_id": "7509353598782816256", "api_name": "xz_zgjm", "plugin_version": "0", "is_draft": false, "fc_setting": {"plugin_id": "7509353177339133952", "api_id": "7509353598782816256", "api_name": "xz_zgjm", "request_params": [{"id": "Js8dCSLqpl", "name": "title", "desc": "查询解梦标题，例如：梦见蛇", "type": 1, "location": 2, "is_required": true, "sub_parameters": null, "global_disable": false, "local_disable": false, "enum_list": null, "enum_var_names": null}, {"id": "byhjWylhTV", "name": "input_string", "desc": "input_string", "type": 1, "location": 2, "is_required": true, "sub_parameters": null, "global_disable": false, "local_disable": false, "enum_list": null, "enum_var_names": null}], "response_params": [{"id": "v4LbpIna8l", "name": "err_msg", "desc": "错误提示", "type": 1, "location": 3, "is_required": false, "sub_parameters": null, "global_disable": false, "local_disable": false, "enum_list": null, "enum_var_names": null}, {"id": "8b0GUYl7Gb", "name": "data_structural", "desc": "返回数据结构", "type": 4, "location": 3, "is_required": true, "sub_parameters": [{"id": "SnyG0gON9c", "name": "title", "desc": "解梦标题", "type": 1, "location": 3, "is_required": false, "sub_parameters": null, "global_disable": false, "local_disable": false, "enum_list": null, "enum_var_names": null}, {"id": "iZa8hLqUML", "name": "weburl", "desc": "当前内容关联的页面地址", "type": 1, "location": 3, "is_required": false, "sub_parameters": null, "global_disable": false, "local_disable": false, "enum_list": null, "enum_var_names": null}, {"id": "uWcirBuvs-", "name": "content", "desc": "解梦内容", "type": 1, "location": 3, "is_required": false, "sub_parameters": null, "global_disable": false, "local_disable": false, "enum_list": null, "enum_var_names": null}], "global_disable": false, "local_disable": false, "enum_list": null, "enum_var_names": null}, {"id": "eXvWWTrzMa", "name": "data", "desc": "返回数据", "type": 1, "location": 3, "is_required": true, "sub_parameters": null, "global_disable": false, "local_disable": false, "enum_list": null, "enum_var_names": null}], "response_style": {"mode": 0}, "is_draft": false, "plugin_version": "0"}}]}}, "settingOnError": {}}, "outputs": [{"type": "string", "name": "output"}], "version": "3"}}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "120238"}, {"sourceNodeID": "120238", "targetNodeID": "900001"}]}