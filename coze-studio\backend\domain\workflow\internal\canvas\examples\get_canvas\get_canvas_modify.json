{"nodes": [{"id": "100001", "type": "1", "meta": {"position": {"x": 236, "y": -246.7}}, "data": {"nodeMeta": {"description": "", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "start"}, "outputs": [{"type": "string", "name": "input", "required": false}, {"type": "string", "name": "e", "required": true}, {"type": "string", "name": "t", "required": true}], "trigger_parameters": [{"type": "string", "name": "input", "required": false}, {"type": "string", "name": "e", "required": true}]}}, {"id": "900001", "type": "2", "meta": {"position": {"x": 858, "y": -286.7}}, "data": {"nodeMeta": {"description": "", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "end"}, "inputs": {"terminatePlan": "returnVariables", "inputParameters": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "input"}, "rawMeta": {"type": 1}}}}, {"name": "e", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "e"}, "rawMeta": {"type": 1}}}}, {"name": "t", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "t"}, "rawMeta": {"type": 1}}}}]}}}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "900001"}], "versions": {"loop": "v2"}}